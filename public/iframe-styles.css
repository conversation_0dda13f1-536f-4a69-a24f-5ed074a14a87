/* LiveChat iframe 样式文件 */

/* 基础 iframe 样式 */
.fsLiveChat {
  position: fixed;
  width: 392px;
  height: 632px;
  bottom: 32px;
  right: -408px;
  z-index: 99;
  transition: right 0.3s ease-in-out;
  border: none;
  opacity: 0;
  /* 可选的圆角和阴影效果 */
  /* border-radius: 8px; */
  /* box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.15); */
}

/* 显示状态样式 */
.fsLiveChat.show {
  right: 68px;
  opacity: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .fsLiveChat {
    width: 100%;
    height: 100%;
    bottom: 0;
    right: -100%;
  }
  
  .fsLiveChat.show {
    right: 0;
    z-index: 99999;
  }
}

/* 低高度屏幕适配 */
@media (max-height: 700px) {
  .fsLiveChat {
    bottom: 0;
    height: 100%;
  }
}

/* 加载状态样式 */
.fsLiveChat.loading {
  opacity: 0.5;
  pointer-events: none;
}

/* 错误状态样式 */
.fsLiveChat.error {
  border: 2px solid #ff4444;
  opacity: 0.8;
}

/* 动画效果增强 */
.fsLiveChat {
  transform: translateZ(0); /* 启用硬件加速 */
  backface-visibility: hidden;
}

/* 自定义滚动条样式（如果iframe内容需要） */
.fsLiveChat::-webkit-scrollbar {
  width: 6px;
}

.fsLiveChat::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.fsLiveChat::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.fsLiveChat::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
