.live_chat_footer {
  min-height: 66px;
  width: 100%;
  flex-shrink: 0;
  // box-shadow: 0px -2px 12px 0px rgba(195, 195, 206, 0.24);
  padding: 0 16px;
  position: relative;

  .live_chat_footer_disable {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0.4;
    background: #fff;
    cursor: not-allowed;
  }
  .privacy_policy_wrap {
    padding: 0 12px 12px 12px;
    cursor: pointer;
    .text {
      font-size: 9px;
      line-height: 15px;
      color: #707070;
      &:hover {
        color: #19191a;
      }
    }
  }

  .footer_file_wrap {
    padding: 18px 0;

    .footer_file_box {
      display: flex;
      align-items: center;

      .footer_file_item {
        position: relative;
        padding: 8px 12px;
        border: 1px solid #cccccc;
        margin-right: 12px;
        border-radius: 2px;

        &:last-child {
          margin-right: 0;
        }

        .file_name {
          color: #19191a;
          font-size: 13px;
          word-break: break-all;
        }

        .iconfont_delete {
          position: absolute;
          color: #999999;
          display: inline-block;
          width: 16px;
          height: 16px;
          font-size: 16px;
          right: -8px;
          top: -8px;
          cursor: pointer;
          border-radius: 50%;
          z-index: 8;
          background: #fff;
        }
      }
    }

    .files_error {
      @include font14;
      color: #c00000;
      margin-top: 10px;
    }
  }

  .footer_message_box {
    position: relative;
    display: flex;
    // justify-content: space-between;
    align-items: center;
    padding: 12px 0 12px;
    .input_wrap {
      flex: 1;
      position: relative;
      .icon_emoji {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 18px;
      }
    }

    // @media (max-width: 1024px) {
    //   padding: 14px 0;
    // }
    :deep(.menu_pop_wrap) {
      position: absolute;
      left: -8px;
      border-radius: 3px;
      background: #fff;
      box-shadow: 0 1px 8px 0 rgb(120 102 102 / 30%);
      transition: all 0.3s;
      z-index: 99;

      .arrow {
        position: absolute;
        transform: rotate(45deg);
        left: 13px;
        bottom: -8px;
        width: 15px;
        height: 15px;
        background-color: #fff;
        border-bottom: 1px solid #e5e5e5;
        border-right: 1px solid #e5e5e5;
      }

      .content_wrap {
        height: 100%;
        padding: 0 16px;
      }
    }

    .textarea {
      width: 100%;
      @include font13;
      color: #19191a;
      padding: 11px 12px;
      padding-right: 36px;
      // border: none;
      border-radius: 6px;
      max-height: 96px;
      transition: none;
      overflow: hidden;
      scrollbar-width: thin;

      // @media (max-width: 1024px) {
      //     margin-bottom: 8px;
      // }
      &::-webkit-scrollbar {
        width: 6px;
        height: 51px;
        background: #f7f7f7;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: #d8d8d8;
      }

      &::-webkit-scrollbar-track {
        border-radius: 10px;
        background: #ffffff;
      }
    }

    .icon {
      display: flex;
      height: 24px;
      width: 24px;
      color: #707070;
      // border-radius: 50%;
      align-items: center;
      justify-content: center;
      // background: #f5f5f9;
      cursor: pointer;
      position: relative;

      &:hover {
        // background-color: #4B4B4D;
        color: #19191A;
      }
    }

    .icon_add {
      font-size: 14px;
      margin-right: 8px;
      // color: #707070;
      // @media (max-width: 1024px) {
      //   height: 38px;
      //   width: 38px;
      // }
    }

    .btn_box {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      padding-left: 8px;

      .icon_feedback {
        // &:hover {
        //   .bg_icon {
        //     background-image: url('@/assets/svg/feedback_hover.svg') ;
        //   }
        // }
        .bg_icon {
          width: 20px;
          height: 20px;
          background-image: url('@/assets/svg/feedback.svg');
          background-repeat: no-repeat;
          background-position: center center;
          background-size: contain;
          &:hover {
            background-image: url('@/assets/svg/feedback_hover.svg') ;
          }
        }
      }
      .icon_emoji {
        margin-right: 8px;
        font-size: 18px;
        // @media (max-width: 1024px) {
        //   height: 38px;
        //   width: 38px;
        // }
      }

      .iconfont_file {
        font-size: 16px;
        margin-right: 8px;

        // @media (max-width: 1024px) {
        //   height: 38px;
        //   width: 38px;
        // }
        >input {
          padding: 0;
          position: absolute;
          display: block;
          opacity: 0;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          bottom: 0;
          right: 0;
          font-size: 18px;

          &::-webkit-file-upload-button {
            cursor: pointer;
          }
        }

        .file_tip {
          // display: none;
          display: none;
          position: absolute;
          padding: 8px 12px;
          color: #fff;
          @include font14;
          border-radius: 4px;
          background: #19191a;
          white-space: nowrap;
          top: -48px;
          left: 50%;
          transform: translate3d(-50%, 0, 0);
          cursor: default;

          &::before {
            display: block;
            content: '';
            position: absolute;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            bottom: -15px;
            left: 50%;
            border: 8px solid transparent;
            border-top: 8px solid #19191a;
          }
        }

        &:hover {
          .file_tip {
            display: block;

            @media (max-width: 960px) {
              display: none;
            }
          }
        }
      }

      .iconfont_send {
        font-size: 20px;
        // background: #4b4b4d;
        color: #707070;

        // @media (max-width: 1024px) {
        //   height: 38px;
        //   width: 38px;
        // }
        &:hover {
          // background-color: #19191a;
          color: #19191a;
        }

        // &.iconfont_send_active {
        //     background: #c00000;
        //     color: #fff;
        // }
      }
    }
  }
}