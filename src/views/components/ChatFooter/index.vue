<template>
  <section class="live_chat_footer" v-if="appCode !== '2'">
    <div class="footer_file_wrap" v-if="uploadFileList.length || fileError">
      <div
        class="footer_file_box"
        v-if="uploadFileList && uploadFileList.length"
      >
        <div
          class="footer_file_item"
          v-for="(item, index) in uploadFileList"
          :key="index"
        >
          <span class="file_name">{{ item.formatName || item.fileName }}</span>
          <span class="iconfont iconfont_delete" @click="deleteFile(index)"
            >&#xf018;</span
          >
        </div>
      </div>
      <div class="files_error" v-if="fileError">{{ fileError }}</div>
    </div>

    <!-- 底部输入框 -->
    <DropDown direction="up">
      <Emoji @emoji-click="emojiSelect" v-show="showDropdown"></Emoji>
    </DropDown>
    <div class="footer_message_box">
      <MenuPopWrap
        v-show="showMenuPop"
        @hide="showMenuPop = false"
        @change-mute="changeMute"
        @show-address="$emit('showAddress')"
      ></MenuPopWrap>
      <!-- <div class="btn_left">
        <span
          class="iconfont icon_add icon"
          @click.stop="changeShowMenuPop"
        >{{ showMenuPop ? "&#xf041;" : "&#xf068;" }}</span>
      </div> -->
      <!-- <div class="btn_left" @click="handleMenuShow('option')">
                          <span class="iconfont icon_add icon">{{ menuVisible && menuType === "option" ? "&#xf041;" : "&#xf068;" }}</span>
                    </div> -->
      <div class="input_wrap">
        <textarea
          autocomplete="“off”"
          ref="messageTextarea"
          class="textarea"
          rows="1"
          maxlength="1000"
          :placeholder="$c('placeholder2')"
          @input.stop="textareaInput"
          :style="{ height: `42px` }"
          v-model.trim="textareaValue"
          @keydown.enter.stop="textareaEnter"
          :disabled="footerDisable"
          @focus="handleTextareaFocus"
          @blur="handleTextareaBlur"
        />
        <!-- emoji按钮 -->
        <span class="iconfont icon_emoji icon" @click="changeEmojiShow">{{
          showDropdown ? "&#xf278;" : "&#xf279;"
        }}</span>
      </div>
      <div class="btn_box">
        <span class="iconfont iconfont_file icon"
          >&#xf191;
          <input
            type="file"
            :accept="acceptFile.join(',')"
            title=""
            multiple
            @change="fileChange"
          />
          <div class="file_tip">{{ $c("fileType") }}</div>
        </span>
        <!-- 评价按钮 -->
        <span
          class="iconfont icon_feedback icon"
          v-if="newFeedbackIcon && showFeedbackIcon"
          @click="$emit('onFeedbackClick')"
        >
          <div class="bg_icon"></div>
        </span>
        <span
          v-else
          class="iconfont iconfont_send icon"
          @click.stop="sendMessage"
          @mousedown="preventBlur"
          >&#xe6a2;</span
        >
      </div>
    </div>
    <!-- <DropDown direction="up">
      <Emoji @emoji-click="emojiSelect" v-show="showDropdown"></Emoji>
    </DropDown> -->
    <div class="live_chat_footer_disable" v-if="footerDisable"></div>
    <!-- <div class="privacy_policy_wrap" @click="handlePrivacyPolicyClick">
      <p class="text">
        This conversation will be monitored and recorded. Any information shared
        is subject to Privacy Policy.
      </p>
    </div> -->
  </section>
  <!-- app底部 -->

  <MobileFooter
    v-else
    @mobileFooterInput="textareaInput"
    @mobileFooterSend="sendMessage"
    @mobileFileChange="fileChange"
    :acceptFile="acceptFile"
    :footerDisabled="footerDisable"
    @mobileFilesSend="mobileFilesSend"
  />
</template>

<script setup lang="ts">
import {
  ref,
  inject,
  watch,
  nextTick,
  onMounted,
  getCurrentInstance,
} from "vue";
import EventBus from "@/util/eventBus";
import { dataLayerToParent } from "@/util/util";

import { post } from "@/util/request";

import MobileFooter from "@/views/components/MobileFooter/index.vue";
import MenuPopWrap from "@/components/MenuPopWrap.vue";
import DropDown from "@/components/DropDown.vue";
import Emoji from "@/components/Emoji.vue";

const $c = getCurrentInstance().proxy.$c;

export type MessagePayload = {
  text: string;
  files: FileInfoType[];
};

type ChatFooterProps = {
  appCode: string;
  siteCode: string;
  messageList: MessageBody[];
  footerDisable: boolean;
  newFeedbackIcon?: boolean;
};
const props = withDefaults(defineProps<ChatFooterProps>(), {
  appCode: "1",
  siteCode: "en",
  messageList: () => [],
  footerDisable: false,
  newFeedbackIcon: false,
});
const $bus = inject<EventBus>("eventBus");
const emit = defineEmits<{
  (e: "changeMute", status: boolean): void;
  (e: "showAddress"): void;
  (e: "sendMessage", val: MessagePayload): void;
  (e: "messageListChange", val: any): void;
  (e: "onPreviewMsg", val: string): void;
  (e: "onFeedbackClick"): void;
}>();

const acceptVideo = ["mp4", "avi", "mov"];
const acceptImg = ["jpeg", "jpg", "png", "heic", "heif"];
const acceptDoc = ["pdf", "doc", "docx", "xls", "xlsx", "txt"];
const acceptFile = [
  ".pdf",
  ".doc",
  ".docx",
  ".xls",
  ".xlsx",
  ".jpeg",
  ".jpg",
  ".png",
  ".heic",
  ".heif",
  ".txt",
  ".mp4",
  ".avi",
  ".mov",
];
const textareaValue = ref("");
const uploadFileList = ref<FileInfoType[]>([]);
const fileError = ref("");
const showMenuPop = ref(false);
const showDropdown = ref(false);
const messageTextarea = ref<HTMLTextAreaElement>();

$bus.on("clearTextValue", () => {
  console.log("-------");
  textareaValue.value = "";
});

const handlePrivacyPolicyClick = () => {
  window.open("https://www.fs.com/policies/privacy_policy.html", "_blank");
};

const showFeedbackIcon = ref(true);
let preventBlurFlag = false;
const handleTextareaFocus = () => {
  showFeedbackIcon.value = false;
};

const handleTextareaBlur = () => {
  if (preventBlurFlag) {
    preventBlurFlag = false;
    return;
  }
  showFeedbackIcon.value = true;
};

const preventBlur = (e: MouseEvent) => {
  preventBlurFlag = true;
};

// 删除文件
const deleteFile = (index: number) => {
  uploadFileList.value.splice(index, 1);
};

const changeMute = (status: boolean) => {
  emit("changeMute", status);
};
const changeShowMenuPop = () => {
  showMenuPop.value = !showMenuPop.value;
  dataLayerToParent({
    eventLabel: "more features",
  });
};
const changeEmojiShow = (e: Event) => {
  showDropdown.value = !showDropdown.value;
};

// 处理点击Emoji组件外面隐藏Emoji组件
const handleEmojiClickOutside = () => {
  console.log('handleEmojiClickOutside');
  if (showDropdown.value) {
    showDropdown.value = false;
  }
};

const textareaInput = (e: Event) => {
  const target = e.target as HTMLInputElement;
  textareaValue.value = target.value;
};
// 输入文字，按下shift+enter换行，按下回车发送消息
const textareaEnter = (e: KeyboardEvent) => {
  if (!e.shiftKey && e.code === "Enter" && !e.isComposing) {
    e.preventDefault();
    sendMessage();
  }
};

// 新增首次发送消息时，埋点
const firstSendMessage = ref(false);

const sendMessage = () => {
  props.appCode !== "2" && messageTextarea.value?.focus();
  if (!firstSendMessage.value) {
    firstSendMessage.value = true;
    dataLayerToParent({
      eventLabel: `write_${textareaValue.value}`,
    });
  }
  emit("sendMessage", {
    text: textareaValue.value,
    files: uploadFileList.value,
  });
};
// 文件选择
const fileChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  const files = target.files;
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const fileName = file.name;
    const fileExt = fileName
      .substring(fileName.lastIndexOf(".") + 1)
      .toLowerCase();
    if (acceptImg.includes(fileExt)) {
      if (file.size > 5 * 1024 * 1024) {
        // 超过5M
        fileError.value = $c("fileSize5M");
      } else if (file.size === 0) {
        // 禁止空文件上传
        fileError.value = "Please do not upload empty files";
      } else {
        uploadFile(file);
      }
    } else if (acceptDoc.includes(fileExt) || acceptVideo.includes(fileExt)) {
      if (file.size > 20 * 1024 * 1024) {
        // 超过20M
        fileError.value = $c("fileSize20M");
      } else {
        uploadFile(file);
      }
    }
  }
};
// 上传文件
const uploadFile = async (file: File) => {
  const formData = new FormData();
  console.log(file);
  formData.append("file", file);
  const res: any = await post("/livechat/upload-file", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  let fileSize = "";
  if (file.size < 1024 * 1024) {
    fileSize = (file.size / 1024).toFixed(2) + "KB";
  } else {
    fileSize = (file.size / 1024 / 1024).toFixed(2) + "MB";
  }
  let fileParams: FileInfoType = {
    fileUrl: res.url,
    fileName: file.name,
    fileSize,
    fileExt: file.name.substring(file.name.lastIndexOf(".") + 1).toLowerCase(),
  };
  if (file.name.length > 10) {
    fileParams.formatName =
      file.name.slice(0, 4) + "..." + file.name.slice(file.name.length - 4);
  }
  uploadFileList.value.push(fileParams);
  if (props.appCode === "2") {
    emit("sendMessage", {
      text: textareaValue.value,
      files: uploadFileList.value,
    });
  }
  fileError.value = "";
};
const emojiSelect = (emoji: string) => {
  preventBlurFlag = true;
  insertAtCursor(messageTextarea.value, emoji);
  showDropdown.value = false;
};
// 将选择的emoji表情插入到输入框的光标之后
const insertAtCursor = (myField: HTMLTextAreaElement, myValue: string) => {
  if (myField.selectionStart || myField.selectionStart === 0) {
    const startPos = myField.selectionStart;
    const endPos = myField.selectionEnd;
    const restoreTop = myField.scrollTop;
    myField.value =
      myField.value.substring(0, startPos) +
      myValue +
      myField.value.substring(endPos, myField.value.length);
    if (restoreTop > 0) {
      myField.scrollTop = restoreTop;
    }
    myField.focus();
    myField.selectionStart = startPos + myValue.length;
    myField.selectionEnd = startPos + myValue.length;
  } else {
    myField.value += myValue;
    myField.focus();
  }
  textareaValue.value = myField.value;
};
// 调节输入框高度
const adjustTextareaHeight = (cb: () => void) => {
  nextTick(() => {
    const target = messageTextarea.value;
    if (target?.style) {
      target.style.height = "auto";
      target.style.height = target.scrollHeight + "px";
    }
    cb && cb();
  });
};

const mobileFilesSend = (info: any) => {
  const { fileName, fileSize } = info;
  let formatFileSize = "";
  if (info.size < 1024 * 1024) {
    formatFileSize = (info.size / 1024).toFixed(2) + "KB";
  } else {
    formatFileSize = (info.size / 1024 / 1024).toFixed(2) + "MB";
  }
  let fileParams = {
    ...info,
    fileSize: formatFileSize,
    originSize: fileSize,
  };
  if (fileName.length > 10) {
    fileParams.formatName =
      fileName.slice(0, 4) + "..." + fileName.slice(fileName.length - 4);
  }
  console.log("fileParams", fileParams);
  uploadFileList.value.push(fileParams);
  sendMessage();
};
watch(
  () => textareaValue.value,
  (val) => {
    emit("onPreviewMsg", val);
    adjustTextareaHeight(() => {
      if (!messageTextarea.value) return;
      if (messageTextarea.value.scrollHeight > 94) {
        messageTextarea.value.style.overflow = "auto";
      } else {
        messageTextarea.value.style.overflow = "hidden";
      }
    });
  }
);

/**
 * @description: 发送消息
 * @return {*}
 */
// const sendMessage = () => {
//   if (!textareaValue.value && !uploadFileList.value.length) return
//   let filterValue = textareaValue.value
//   if (['cn', 'hk', 'mo', 'tw'].includes(props.siteCode)) {
//     filterValue = filterSensitiveWords(textareaValue.value)
//   }
//   const formatTextareaValue = xssFilter(filterValue)
//   const hideMsgList = checkHideSelfServe()
//   let messageType = 2
//   let msgBody = {
//     msg: '',
//     messageTime: formatDate(new Date().getTime(), 'en'),
//     ...defaultMsgBody
//   }
//   let msgParams: Record<string, any> = { clientMessageId: generateUUID() }
//   const registerInfo = JSON.parse(localStorage.getItem('register_info'))
//   // 文本消息
//   if (textareaValue.value && !uploadFileList.value.length) {
//     msgBody.msg = formatTextareaValue
//     if (registerInfo?.step) {
//       stepRecord.value = registerInfo.step
//     }
//     if (stepRecord.value === 1) { //留资步骤1
//       messageType = 23
//       msgParams.email = formatTextareaValue
//       localStorage.setItem('register_info', JSON.stringify({ step: stepRecord.value, email: msgParams.email }))
//       ws.sendMsg(msgParams, { messageType })
//     } else if (stepRecord.value === 2) { //留资步骤2
//       messageType = 24
//       msgParams.name = formatTextareaValue
//       msgParams.email = registerInfo.email
//       msgParams.customerServiceTypeId = customerServiceTypeId
//       ws.sendMsg(msgParams, { messageType })
//     } else {
//       msgParams = {
//         groupId: '',
//         msg: formatTextareaValue,
//         ...msgParams
//       }
//       hideMsgList ? msgParams.hideMsgList = hideMsgList : ''
//       ws.sendMsg(msgParams, { messageType })
//     }
//     textareaValue.value = ''
//     messageList.value.push({ ...msgBody, ...msgParams })
//   }
//   // 文件消息
//   if (uploadFileList.value.length) {

//     uploadFileList.value.forEach(item => {
//       let params = {
//         fileInfo: {
//           ...item
//         },
//         clientMessageId: generateUUID()
//       }
//       ws.sendMsg(params, { messageType })
//       messageList.value.push({ ...msgBody, ...params })
//     })
//     uploadFileList.value.splice(0, uploadFileList.value.length)
//   }
//   // scrollToBottom()
// }

// 检查是否隐藏自服务标签
// const checkHideSelfServe = () => {
//   const filterList = props.messageList.filter(item => [19, 45].includes(item.messageType))
//   if (filterList.length > 0) {
//     const hideMsgList = filterList.map(item => String(item.messageId))
//     return hideMsgList
//   }
// }
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
