<template>
  <template v-for="(item, i) in messageList">
    <div
      class="sensitive_warning"
      v-if="[82, 83].includes(item.messageType)"
    >
      <div class="warning_text">
        {{ item.msg }}
      </div>
    </div>
    <!-- whatsapp接入 -->
    <div
      class="wa_wrap"
      v-if="item.messageType === 123"
    >
      <div class="link_text">
        You can chat with sales via
        <a
          href="https://wa.me/6564437951"
          target="_blank"
          rel="noopener noreferrer"
        >WhatsApp</a>.
      </div>
      <div class="des_text">
        <div class="text">or fill out the form below</div>
      </div>
    </div>
    <!-- 系统消息 -->
    <div
      class="message_sys_wrap message_wrap"
      v-if="[3, 20, 26, 30, 37, 43].includes(item.messageType)"
      :key="i"
    >
      <div
        class="message_create_box"
        v-if="item.messageType === 3"
      >
        <p class="message_chat">
          {{ item.msg + " " + utcToLocal(item.messageTime) }}
        </p>
      </div>
      <div
        class="message_create_box"
        v-if="item.messageType === 20"
      >
        <p
          class="message_chat privacy"
          v-html="item.msg"
        ></p>
      </div>
      <div
        class="message_sys_box"
        v-if="[26, 30, 37, 43].includes(item.messageType)"
      >
        <p class="message_admin_time">{{ utcToLocal(item.messageTime) }}</p>
        <div class="message_admin_msg_box">
          <span class="iconfont iconfont_success">
            <img
              src="@/assets/svg/confirmed.svg"
              alt=""
            />
          </span>
          <p class="message_admin_msg">{{ item.msg }}</p>
        </div>
      </div>
    </div>
    <!-- SG站点，客服分配头像展示-->
    <div
      class="sg_message_info"
      v-if="item.messageType === 48"
      :key="i"
    >
      <div class="top">
        <span
          class="cs_avator"
          :style="{ backgroundImage: `url(${item.avatar})` }"
        >
          <div class="cs_online_dot"></div>
        </span>
        <span class="cs_name">{{ item.customerServiceName }}</span>
      </div>
      <!-- <p class="message_chat">Chat started at {{ item.messageTime }}</p> -->
    </div>

    <!-- 客服消息，左边展示 -->
    <div
      class="message_cs_wrap message_wrap"
      :class="{ 'consecutive-message': isConsecutiveMessage(i) }"
      v-if="
        [21, 33, 38, 46, 47, 49, 54, 77].includes(item.messageType) ||
        (item.messageType === 76 && item.formType === 2)
      "
      :key="i"
    >
      <!-- 判断是否显示头像 -->
      <span
        class="cs_avator"
        :style="{ backgroundImage: `url(${item.avatar})` }"
        v-if="shouldShowAvatar(i)"
      ></span>
      <!-- 添加占位符保持布局 -->
      <span
        class="cs_avator_placeholder"
        v-else
      ></span>
      <div class="message_cs_box">
        <!-- 名称和时间只在头像显示时展示 -->
        <div
          class="cs_name"
          v-if="shouldShowName(i)"
        >
          <span :style="{ marginRight: '4px' }">{{ item.name }}</span>
          <!-- <span>{{ utcToLocal(item.messageTime) }}</span> -->
        </div>
        <!-- 其他内容保持不变 -->
        <div
          class="cs_text_loading"
          v-if="item.isLoading"
        >
          <MessageLoading />
        </div>
        <div
          class="cs_content_wrap"
          v-else
        >
          <div
            class="cs_text"
            v-html="extractUrl(item.msg)"
            v-if="item.msg"
          ></div>
          <template v-if="item.fileInfo">
            <div
              class="cs_img"
              :style="{ backgroundImage: `url(${item.fileInfo.fileUrl})` }"
              v-if="acceptImg.includes(item.fileInfo.fileExt)"
              @click.stop="reviewImg(item.fileInfo)"
            ></div>
            <div
              class="cs_video"
              v-if="acceptVideo.includes(item.fileInfo.fileExt)"
            >
              <video
                :src="item.fileInfo.fileUrl"
                controls
              ></video>
            </div>
            <div
              class="cs_file"
              v-if="acceptDoc.includes(item.fileInfo.fileExt)"
              @click.stop="downloadFile(item.fileInfo)"
            >
              <img
                class="file_icon"
                :src="getAssetURL(`${fileExt[item.fileInfo.fileExt as keyof typeof fileExt]}.svg`)"
                alt=""
              />
              <!-- <img class="file_icon" src="../assets/doc.svg" alt="" /> -->
              <div class="file_info">
                <p class="file_name">{{ item.fileInfo.fileName }}</p>
                <p class="file_size">{{ item.fileInfo.fileSize }}</p>
              </div>
            </div>
          </template>
          <!-- hover显示时间 -->
          <div
            class="cs_message_time"
            :style="{ right: item.messageTime.length >= 9 ? '-60px' : '-50px' }"
          >
            {{ utcToLocal(item.messageTime) }}
          </div>
        </div>
      </div>
    </div>
    <!-- sg站点,客服分配留资表单 -->
    <div
      class="sg_form_wrap"
      v-if="
        ([64].includes(item.messageType) && item.formType === 2) ||
        (item.requestType === 64 && item.formType === 2)
      "
      :key="item.messageId"
    >
      <div class="input_wrap">
        <span class="label">Email address (Optional)</span>
        <div :style="{ position: 'relative' }">
          <input
            type="text"
            placeholder="Enter email here..."
            v-model="item.email"
            @keydown.enter="submitEmail(item)"
            @input="(e) => handleInputEmail(e, item)"
            :disabled="item.messageType === 51"
          />
          <i
            class="iconfont suffix"
            @click="submitEmail(item)"
            v-show="item.suffixStatus === 2"
          >&#xf203;</i>
          <i
            class="iconfont suffix success"
            v-show="item.suffixStatus === 1"
          >&#xf262;</i>
          <i
            class="iconfont suffix error"
            v-show="item.suffixStatus === 0"
          >&#xf041;</i>
          <ValidateError :error="item.applyError.email"></ValidateError>
        </div>
      </div>
    </div>
    <!-- cn站点,客服留资 -->
    <div
      class="form_wrap"
      v-if="
        ([64].includes(item.messageType) && item.formType === 1) ||
        (item.requestType === 64 && item.formType === 1)
      "
      :key="item.messageId"
    >
      <div class="input_wrap">
        <span class="label">{{ $c("formLabel.name") }}</span>
        <input
          type="text"
          v-model="item.customerName"
          :disabled="item.messageType === 51"
        />
        <ValidateError :error="item.applyError.name"></ValidateError>
      </div>
      <div class="input_wrap">
        <span class="label">{{ $c("formLabel.tel") }}</span>
        <div class="tel_code tel_code_cn">
          <div class="code">
            <div class="code_label">+86</div>
          </div>
          <input
            class="tel"
            type="text"
            v-model="item.mobile"
            :disabled="item.messageType === 51"
          />
        </div>
        <ValidateError
          :error="item.applyError.mobile"
          v-if="item.applyError"
        ></ValidateError>
      </div>
      <div class="input_wrap">
        <span class="label">{{ $c("formLabel.email") }}</span>
        <input
          type="text"
          v-model="item.email"
          :disabled="item.messageType === 51"
        />
        <ValidateError
          :error="item.applyError.email"
          v-if="item.applyError"
        ></ValidateError>
      </div>
      <FsButton
        type="black"
        class="form_submit"
        @click="submitApplyRetention(item)"
        :disabled="
          item.messageType === 51 || (!item.customerName && !item.mobile)
        "
      >
        {{ $c("submit") }}
      </FsButton>
    </div>
    <!-- sg,cn留言表单 -->
    <div
      class="form_wrap"
      v-if="[54].includes(item.messageType) || item.requestType === 54"
      :key="item.messageId"
    >
      <div
        class="input_wrap"
        v-if="item.isShowName"
      >
        <span class="label">{{ $c("formLabel.name") }}</span>
        <input
          type="text"
          v-model="item.customerName"
          :disabled="item.messageType === 51"
        />
        <ValidateError :error="leaveMessageError.name"></ValidateError>
      </div>
      <div
        class="input_wrap"
        v-if="item.isShowEmail && siteCode === 'sg'"
      >
        <span class="label">{{ $c("formLabel.email") }}</span>
        <input
          type="text"
          v-model="item.email"
          :disabled="item.messageType === 51"
        />
        <ValidateError :error="leaveMessageError.email"></ValidateError>
      </div>
      <div
        class="input_wrap"
        v-if="item.isShowMobile && ['cn', 'hk', 'mo', 'tw'].includes(siteCode)"
      >
        <span class="label">{{ $c("formLabel.tel") }}</span>
        <input
          type="text"
          v-model="item.mobile"
          :disabled="item.messageType === 51"
        />
        <ValidateError :error="leaveMessageError.mobile"></ValidateError>
      </div>
      <div class="input_wrap">
        <span class="label">{{ $c("formLabel.help") }}</span>
        <textarea
          cols="30"
          rows="10"
          v-model="item.leaveMsg"
          :disabled="item.requestType === 54"
          v-if="item.hasOwnProperty('leaveMsg')"
        ></textarea>
        <textarea
          cols="30"
          rows="10"
          v-model="item.msg"
          disabled
          v-else-if="item.requestType === 54"
        ></textarea>
        <ValidateError :error="leaveMessageError.leaveMsg"></ValidateError>
      </div>
      <FsButton
        type="black"
        class="form_submit"
        @click="submitLeaveMsg(item)"
        :disabled="item.messageType === 51"
      >
        {{ $c("submit") }}
      </FsButton>
    </div>
    <!-- 76: 详情页卡片 -->
    <div
      class="form_wrap"
      v-if="item.messageType === 76"
      :key="i"
    >
      <div class="prod_head">
        <img
          :src="item.extraData.img"
          class="prod_img"
        />
        <div class="title_wrap">
          <div class="txt_container">
            <p class="prod_title">{{ item.extraData.title }}</p>
          </div>
          <span class="prod_id">#{{ item.extraData.id }}</span>
        </div>
      </div>
      <div class="qa_container">
        <div
          class="qa_wrap"
          v-for="t in item.extraData.questionAnswerList"
          :key="t.id"
          @click="selectProdQa(t)"
        >
          <div class="dot"></div>
          <div class="txt_container" :title="t.content">
            <p class="qa_text">
              {{ t.content }}
            </p>
          </div>
        </div>
      </div>
      <div
        class="video_container"
        v-if="item.extraData.videoInfo"
      >
        <div class="divider"></div>
        <div class="video_wrap">
          <div
            class="slide_item"
            @click.stop="() => videoClick(item.extraData)"
          >
            <div class="img_box">
              <img
                class="img"
                :src="item.extraData.videoInfo.image"
                alt=""
              />
              <div class="time_box">
                <fs-button
                  tabindex="0"
                  @keyup.enter.native.stop=""
                  @click=""
                  :stopPropagation="true"
                  type="video-small"
                >{{ item.extraData.videoInfo.time }}</fs-button>
              </div>
            </div>
            <div class="title_box">
              <div
                href="javascript:;"
                tabindex="-1"
                class="title"
              >
                {{ item.extraData.videoInfo.title }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 用户消息，左边展示 -->
    <div
      class="message_user_wrap message_wrap"
      v-if="[2, 32, 42].includes(item.messageType)"
      :key="item.clientMessageId"
      :class="{ 'consecutive-message': isConsecutiveCustomerMessage(i) }"
    >
      <div class="message_user_box">
        <template v-if="![42].includes(item.messageType)">
          <div
            class="cs_text"
            v-html="extractUrl(item.msg)"
            v-if="item.msg"
          ></div>
        </template>
        <template v-if="item.messageType === 42">
          <div class="cs_text">
            {{ item.comments }}
            <span
              class="iconfont iconfont_good"
              v-if="item.score === 1"
            >&#xe719;</span>
            <span
              class="iconfont iconfont_bad"
              v-if="item.score === 2"
            >&#xe722;</span>
          </div>
        </template>
        <template v-if="item.fileInfo">
          <div
            class="cs_img"
            v-if="acceptImg.includes(item.fileInfo.fileExt)"
            :style="{ backgroundImage: `url(${item.fileInfo.fileUrl})` }"
            @click.stop="reviewImg(item.fileInfo)"
          ></div>
          <div
            class="cs_video"
            v-if="acceptVideo.includes(item.fileInfo.fileExt)"
          >
            <video
              :src="item.fileInfo.fileUrl"
              controls
            ></video>
          </div>
          <div
            class="cs_file"
            v-if="acceptDoc.includes(item.fileInfo.fileExt)"
            @click.stop="downloadFile(item.fileInfo)"
          >
            <img
              class="file_icon"
              :src="getAssetURL(`${fileExt[item.fileInfo.fileExt as keyof typeof fileExt]}.svg`)"
              alt=""
            />
            <!-- <img class="file_icon" src="../assets/doc.svg" alt="" /> -->
            <div class="file_info">
              <p class="file_name">{{ item.fileInfo.fileName }}</p>
              <p class="file_size">{{ item.fileInfo.fileSize }}</p>
            </div>
          </div>
        </template>
        <div
          class="cs_name"
          v-if="messageList.length - 1 === i"
        >
          {{ item.messageReadStatus ? $c("read") : $c("unread") }}
          {{ utcToLocal(item.messageTime) }}
        </div>
        <div class="cs_message_time" v-else>
          {{ utcToLocal(item.messageTime) }}
        </div>
      </div>
    </div>
    <!-- 
              自服务模块
              19：自服务标签
              45：客服分配标签
            -->
    <transition name="robot_serve">
      <div>
        <div
          class="chat_robot_serve"
          v-if="[19, 45].includes(item.messageType)"
          :key="i"
        >
          <ul
            class="tag_wrap"
            v-show="!item.isLoading"
          >
            <li
              class="chat_robot_serve_item"
              v-for="menuItem in item.extraData"
              :key="menuItem.id"
              @click="
                selectServeType(menuItem, item.messageId, item.messageType, i)
              "
            >
              <div
                class="msg_content"
                v-html="menuItem.content"
              ></div>
            </li>
          </ul>
        </div>
        <div
          class="chat_robot_serve"
          v-if="item.messageType === 87"
        >
          <div class="tag_wrap">
            <div class="form_item_wrap">
              <div
                class="chat_form_input"
                :class="{ active_input: focusName }"
              >
                <input
                  type="text"
                  :readonly="freezeForm"
                  autocomplete="off"
                  class="input_el"
                  name="name"
                  @input="(e) => handleInput(e, 'name')"
                  v-model="newUserForm.name"
                />
                <label
                  for="name"
                  class="label_el"
                >{{
                  $c("userForm.name")
                  }}</label>
                <div
                  class="status_icon"
                  v-show="newUserForm.name.length > 0"
                >
                  <i class="iconfont">&#xf262;</i>
                </div>
              </div>
              <div
                class="validate_error"
                v-show="userFormError.name"
              >
                <i class="iconfont">&#xe718;</i>
                <p>{{ userFormError.name }}</p>
              </div>
            </div>
            <div class="form_item_wrap">
              <div
                class="chat_form_input"
                :class="{ active_input: focusEmail }"
              >
                <input
                  type="text"
                  :readonly="freezeForm"
                  autocomplete="off"
                  class="input_el"
                  name="email"
                  @input="(e) => handleInput(e, 'email')"
                  v-model="newUserForm.email"
                />
                <label
                  for="email"
                  class="label_el"
                >{{
                  $c("userForm.email")
                  }}</label>
                <div
                  class="status_icon"
                  v-show="isValidateEmail"
                >
                  <i class="iconfont">&#xf262;</i>
                </div>
              </div>
              <div
                class="validate_error"
                v-show="userFormError.email"
              >
                <i class="iconfont">&#xe718;</i>
                <p>{{ userFormError.email }}</p>
              </div>
            </div>
            <div class="form_item_wrap">
              <div
                class="chat_form_input"
                :class="{ active_input: focusSelect }"
                v-click-outside="handleClickOutside"
              >
                <input
                  readonly
                  type="text"
                  class="input_el"
                  name="select"
                  @click="selectClick"
                  v-model="newUserForm.type.content"
                />
                <label
                  for="select"
                  class="label_el"
                >{{ $c("userForm.selectAgent") }}
                  <i
                    class="iconfont"
                    :class="{ rotate: dropdownStatus }"
                    @click="arrowClick"
                  >&#xe704;</i></label>
                <div
                  class="status_icon"
                  v-show="newUserForm.type.content"
                >
                  <i class="iconfont">&#xf262;</i>
                </div>
                <DropDown>
                  <div
                    class="dropdown_list"
                    v-show="dropdownStatus"
                  >
                    <div
                      class="dropdown_item"
                      v-for="agent in agentList"
                      :key="agent.id"
                      @click="handleAgentSelect(agent)"
                    >
                      {{ agent.content }}
                    </div>
                  </div>
                </DropDown>
              </div>
              <div
                class="validate_error"
                v-show="userFormError.type"
              >
                <i class="iconfont">&#xe718;</i>
                <p>{{ userFormError.type }}</p>
              </div>
            </div>
            <div
              class="submit_btn"
              @click="handleSubmitUserInfo"
              :class="{ freeze: freezeForm }"
            >
              <div class="btn_content">
                <i
                  class="iconfont"
                  v-if="freezeForm"
                >&#xf262;</i>
                <span>{{
                  freezeForm ? $c("userForm.saved") : $c("submit")
                  }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </template>
</template>

<script setup lang="ts">
import {
  reactive,
  getCurrentInstance,
  ref,
  onMounted,
  onBeforeUnmount,
  watch,
  inject,
  computed,
} from "vue";
import Ws from "@/util/FsWs";
import {
  downloadFileByUrl,
  isAndroid,
  utcToLocal,
  getAssetURL,
  validateEmail,
  validatePhone,
  dataLayerToParent,
} from "@/util/util";
import EventBus from "@/util/eventBus";

import MessageLoading from "@/components/MessageLoading.vue";
import ValidateError from "@/components/ValidateError.vue";
import FsButton from "@/components/FsButton.vue";
import DropDown from "@/components/DropDown.vue";

const $c = getCurrentInstance()?.proxy.$c;
const $bus = inject<EventBus>("eventBus");

type MessageItemProps = {
  messageList: MessageBody[];
  appCode: string;
  siteCode: string;
};

const props = withDefaults(defineProps<MessageItemProps>(), {
  messageList: () => [],
  appCode: "1",
  siteCode: "en",
});

const emit = defineEmits<{
  (e: "selectProdQa", item: any): void;
  (
    e: "selectServeType",
    menuItem: ServeType,
    messageId: string,
    msgType: number,
    index: number
  ): void;
  (e: "validate-video-click", item: any): void;
}>();
const ws = Ws.getInstance();
const acceptImg = ["jpeg", "jpg", "png", "heic", "heif"];
const acceptDoc = ["pdf", "doc", "docx", "xls", "xlsx", "txt"];
const acceptVideo = ["mp4", "avi", "mov"];
const fileExt = {
  doc: "doc",
  docx: "doc",
  xls: "xls",
  xlsx: "xls",
  pdf: "pdf",
  txt: "txt",
};
const agentList = [
  {
    content: $c("userForm.selectOption[0]"),
    id: 9,
    level: 0,
    type: 0,
  },
  {
    content: $c("userForm.selectOption[1]"),
    id: 10,
    level: 0,
    type: 0,
  },
];
const newUserForm = ref<{
  [key: string]: any;
}>({
  name: "",
  email: "",
  type: {
    content: "",
    id: 0,
  },
});
const userFormError = ref({
  name: "",
  email: "",
  type: "",
});
const focusName = ref(false);
const focusEmail = ref(false);
const focusSelect = ref(false);
const dropdownStatus = ref(false);
const isValidateEmail = ref(false);
const freezeForm = ref(false);

watch(newUserForm.value, (newVal, oldVal) => {
  isValidateEmail.value = validateEmail(newVal.email.trim());
});

const adjustInput = (type: string) => {
  if (type === "name") {
    if (newUserForm.value.name.length > 0) {
      focusName.value = true;
    } else {
      focusName.value = false;
    }
  }
  if (type === "email") {
    if (newUserForm.value.email.length > 0) {
      focusEmail.value = true;
    } else {
      focusEmail.value = false;
    }
  }
  // if (type ==='select') {
  //   if (newUserForm.value.type.content.length > 0) {
  //     focusSelect.value = true
  //   } else {
  //     focusSelect.value = false
  //   }
  // }
};
const handleInput = (e: Event, type: string) => {
  const target = e.target as HTMLInputElement
  // 如果是邮箱输入，过滤掉空格
  if (type === 'email') {
    const filteredValue = target.value.replace(/\s/g, '')
    target.value = filteredValue
    newUserForm.value[type] = filteredValue
  } else {
    newUserForm.value[type] = target.value
  }
  adjustInput(type)
  validateForm(type)
}
const selectClick = () => {
  if (dropdownStatus.value || freezeForm.value) return;
  focusSelect.value = true;
  dropdownStatus.value = true;
};
const arrowClick = () => {
  dropdownStatus.value = !dropdownStatus.value;
};
const handleAgentSelect = (agent: any) => {
  newUserForm.value.type = agent;
  dropdownStatus.value = false;
  validateForm("type");
};
const handleClickOutside = () => {
  dropdownStatus.value = false;
  if (!newUserForm.value.type.content) {
    focusSelect.value = false;
  }
};
const validateForm = (field: string) => {
  // const { name, email, type } = newUserForm.value
  let validFlag = 0;
  if (field === "name") {
    if (!newUserForm.value.name) {
      userFormError.value.name = $c("formError.help");
      validFlag = 1;
    } else {
      userFormError.value.name = "";
    }
  }
  if (field === "type") {
    if (!newUserForm.value.type.id) {
      userFormError.value.type = $c("formError.help");
      validFlag = 1;
    } else {
      userFormError.value.type = "";
    }
  }
  if (field === "email") {
    if (!newUserForm.value.email.trim()) {
      // 检查邮箱是否为空
      userFormError.value.email = $c("formError.email.error1");
      validFlag = 1;
    } else if (!validateEmail(newUserForm.value.email.trim())) {
      // 检查邮箱格式
      userFormError.value.email = $c("formError.email.error2");
      validFlag = 1;
    } else {
      userFormError.value.email = "";
    }
  }
  if (validFlag !== 0) return false;
  return true;
};
const handleSubmitUserInfo = () => {
  const { name, email, type } = newUserForm.value;
  if (freezeForm.value) return;
  const isValid = Object.keys(newUserForm.value).map((key) => {
    return validateForm(key);
  });
  if (isValid.includes(false)) {
    dataLayerToParent({
      eventLabel: `submit _${
        type.id === 9 ? "product purchase" : "technical support"
      }_fall`,
    });
    return;
  }
  // 处理email前后的空格
  const trimmedEmail = email.trim();
  ws.sendMsg(
    {
      username: name,
      email: trimmedEmail,
      customerServiceTypeId: type.id,
    },
    { messageType: 86 }
  );
  dataLayerToParent({
    eventLabel: `submit _${
      type.id === 9 ? "product purchase" : "technical support"
    }_success`,
  });
  setTimeout(() => {
    freezeForm.value = true;
    $bus.emit("user-form-submit-success");
  }, 500);
};
// 匹配链接
const extractUrl = (text: string) => {
  // 匹配所有a标签
  // 如果有a标签，将a标签内的内容替换为链接
  // 如果没有a标签，将所有链接替换为a标签
  if (text.includes("<a") && text.includes("</a>")) {
    /* eslint-disable */
    const aReg = /<a\b((?![^>]*\btarget=["']?_blank["']?)[^>]*)>/gi;
    const modifyText = text.replace(aReg, '<a$1 target="_blank">');
    return modifyText;
  } else {
    /* eslint-disable */
    const UrlReg =
      /((http|https|ftp):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?)/gi;
    let s = text.replace(UrlReg, `<a href='$1' target="_blank">$1</a>`);
    return s;
  }
};
/**
 * @description 预览图片
 * @param fileInfo 文件信息
 */
const reviewImg = (fileInfo: FileInfoType) => {
  const params = {
    ...fileInfo,
    fileSize: fileInfo.originSize,
  };
  if (props.appCode === "2") {
    console.log(params);
    if (isAndroid()) {
      window.chatJsInterface.previewFile(JSON.stringify(params));
    } else {
      window.livechat.chatJsInterface("previewFile", JSON.stringify(params));
    }
    return;
  }
  const a = document.createElement("a");
  a.href = fileInfo.fileUrl;
  a.target = "_blank";
  a.click();
};

//下载文件
const downloadFile = (fileInfo: FileInfoType) => {
  const { fileUrl, fileName } = fileInfo;
  downloadFileByUrl(fileUrl, fileName);
};
const handleInputEmail = (e: Event, msgItem: any) => {
  const target = e.target as HTMLInputElement;
  target.value.length > 0
    ? (msgItem.suffixStatus = 2)
    : (msgItem.suffixStatus = 3);
};

// 邮箱留资
const submitEmail = (msgItem: MessageBody) => {
  const { email, applyError } = msgItem;
  let validFlag = 0;
  if (!email) {
    applyError.email = "This field is required.";
    validFlag = 1;
  } else if (!validateEmail(email)) {
    applyError.email = "Please enter a valid email address.";
    validFlag = 2;
    msgItem.suffixStatus = 0;
  }
  if (validFlag !== 0) return;
  applyError.email = "";
  const params = {
    email,
    requestType: 49,
  };
  ws.sendMsg(params, { messageType: 50 });
};
// 留资申请填写
const submitApplyRetention = (msg: MessageBody) => {
  const { email, customerName, mobile, applyError } = msg;
  let validFlag = 0;
  if (!customerName) {
    applyError.name = $c("formError.name");
    validFlag = 1;
  }
  if (!mobile) {
    applyError.mobile = $c("formError.tel.error1");
    validFlag = 1;
  } else if (!validatePhone(mobile)) {
    applyError.mobile = $c("formError.tel.error2");
    validFlag = 2;
  }
  if (email && !validateEmail(email)) {
    applyError.email = $c("formError.email.error2");
  }
  if (validFlag !== 0) return;
  applyError.mobile = "";
  applyError.name = "";
  const params = {
    customerName,
    mobile,
    email,
    requestType: 64,
  };
  ws.sendMsg(params, { messageType: 50 });
};

const leaveMessageError = reactive({
  name: "",
  email: "",
  leaveMsg: "",
  mobile: "",
});
// 提交留言
const submitLeaveMsg = (msgItem: MessageBody) => {
  leaveMessageError.name = "";
  leaveMessageError.mobile = "";
  leaveMessageError.email = "";
  leaveMessageError.leaveMsg = "";
  const { email, leaveMsg, customerName, mobile } = msgItem;
  let validFlag = 0;
  if (props.siteCode === "sg") {
    if (!email) {
      leaveMessageError.email = $c("formError.email.error1");
      validFlag = 2;
    } else if (!validateEmail(email)) {
      leaveMessageError.email = $c("formError.email.error2");
      validFlag = 2;
    }
  } else if (["cn", "hk", "mo", "tw"].includes(props.siteCode)) {
    if (!customerName) {
      leaveMessageError.name = $c("formError.name");
      validFlag = 1;
    }
    if (!mobile) {
      leaveMessageError.mobile = $c("formError.tel.error1");
      validFlag = 1;
    } else if (!validatePhone(mobile)) {
      leaveMessageError.mobile = $c("formError.tel.error2");
      validFlag = 1;
    }
    // if (email && !validateEmail(email)) {
    //   leaveMessageError.email = $c('formError.email.error2')
    //   validFlag = 1
    // }
  }

  if (!leaveMsg) {
    leaveMessageError.leaveMsg = $c("formError.help");
    validFlag = 3;
  }
  if (validFlag !== 0) return;
  const params = {
    email,
    customerName,
    msg: leaveMsg,
    requestType: 54,
    mobile,
  };
  ws.sendMsg(params, { messageType: 50 });
};

const selectProdQa = (item: any) => {
  emit("selectProdQa", item);
};
const selectServeType = (
  menuItem: ServeType,
  messageId: string,
  msgType: number,
  index: number
) => {
  emit("selectServeType", menuItem, messageId, msgType, index);
};
const videoClick = (item: any) => {
  window.parent.postMessage(
    { type: "videoClick", data: JSON.stringify(item.videoInfo) },
    "*"
  );
  emit("validate-video-click", item);
};

// 添加判断是否显示头像的方法
const shouldShowAvatar = (index: number) => {
  // 如果是最后一条消息,显示头像
  if (index === props.messageList.length - 1) return true;

  const currentMsg = props.messageList[index];
  const nextMsg = props.messageList[index + 1];

  // 如果下一条消息不是客服消息,显示头像
  if (![21, 33, 38, 46, 47, 49, 54, 77].includes(nextMsg?.messageType))
    return true;

  // 如果下一条消息是不同客服的消息,显示头像
  if (currentMsg.fromUserId !== nextMsg.fromUserId) return true;

  return false;
};

// 判断是否显示名称
const shouldShowName = (index: number) => {
  // 如果是第一条消息,显示名称
  if (index === 0) return true;

  const currentMsg = props.messageList[index];
  const prevMsg = props.messageList[index - 1];

  // 如果上一条消息不是客服消息,显示名称
  if (![21, 33, 38, 46, 47, 49, 54, 77].includes(prevMsg?.messageType))
    return true;

  // 如果上一条消息是不同客服的消息,显示名称
  if (currentMsg.fromUserId !== prevMsg.fromUserId) return true;

  return false;
};

// 判断是否为客服连续消息
const isConsecutiveMessage = (index: number) => {
  if (index === 0) return false;

  const currentMsg = props.messageList[index];
  const nextMsg = props.messageList[index + 1];
  // 如果当前消息和上一条消息都是客服消息，且是同一个客服发送的
  if (
    [21, 33, 38, 46, 47, 49, 54, 77].includes(nextMsg?.messageType) &&
    [21, 33, 38, 46, 47, 49, 54, 77].includes(currentMsg?.messageType) &&
    currentMsg.fromUserId === nextMsg.fromUserId
  ) {
    return true;
  }

  return false;
};
// 判断是否为客户连续消息
const isConsecutiveCustomerMessage = (index: number) => {
  if (index === 0) return false;

  const currentMsg = props.messageList[index];
  const nextMsg = props.messageList[index + 1];
  // 如果当前消息和上一条消息都是客服消息，且是同一个客服发送的
  if (
    [2,32].includes(nextMsg?.messageType) && // 客户消息
    [2,32].includes(currentMsg?.messageType) &&
    currentMsg.fromUserId === nextMsg.fromUserId
  ) {
    return true;
  }

  return false;
};
// 判断是否显示时间
// const showTime = (item: MessageBody) => {
//   // 如果是最后一条消息，显示时间
//   const lastMsg = props.messageList[props.messageList.length - 1]
//   console.log('item', item)
//   console.log('lastMsg', lastMsg)
//   return item.messageId === lastMsg.messageId && item.messageType === 32
// }
</script>

<style scoped lang="scss">
@import "./index.scss";

// 添加占位符样式
.cs_avator_placeholder {
  width: 32px;
  height: 32px;
  flex-shrink: 0;
  margin-right: 8px;
}
</style>
