.sensitive_warning {
  margin: 0 auto;
  margin-bottom: 20px;
  padding: 4px 12px;
  width: fit-content;
  background: #f5f5f9;
  border-radius: 182px;

  .warning_text {
    @include font12;
    color: #707070;
  }
}

.wa_wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;

  .link_text {
    @include font12;
    color: #19191a;
  }

  .des_text {
    margin-top: 8px;

    .text {
      @include font12;
      color: #89898c;
      position: relative;

      &::before,
      &::after {
        position: absolute;
        top: 50%;
        content: "";
        display: inline-block;
        width: 50%;
        height: 1px;
        background-color: #e4e7ed;
      }

      &::before {
        right: 100%;
        margin-right: 12px;
      }

      &::after {
        left: 100%;
        margin-left: 12px;
      }
    }
  }
}

.form_wrap {
  margin: 0 auto;
  margin-bottom: 16px;
  padding: 20px;
  width: 272px;
  font-size: 12px;
  box-sizing: border-box;
  border-radius: 8px;
  // border: 1px solid #e5e5e5;
  box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);

  .input_wrap {
    margin-bottom: 12px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .label {
      display: inline-block;
      margin-bottom: 4px;
    }

    .tel_code {
      width: 100%;
      display: flex;
      align-items: center;
      position: relative;

      &.tel_code_cn {
        .code {
          min-width: auto;
          flex: 0 0 50px;
          text-align: center;
          padding: 0 4px;

          .code_label {
            margin-right: 0;
          }
        }
      }

      .code {
        display: flex;
        align-items: center;
        background: $bgColor1;
        height: 42px;
        border: 1px solid $borderColor2;
        border-right: 0;
        border-radius: 2px 0 0 2px;
        cursor: pointer;
        // min-width: 96px;
        padding: 0 16px;
        flex-shrink: 0;

        .code_label {
          @include font13();
          color: $textColor1;
          flex: 1;
          margin-right: 12px;
        }
      }

      .tel {
        flex: 1 1 auto;
        border-radius: 0 3px 3px 0;
        user-select: text;

        &:focus {
          border-left: 1px solid #19191a;
        }
      }
    }
  }

  .form_submit {
    width: 100%;
    margin-top: 20px;
  }

  .prod_head {
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 16px;

    .prod_img {
      width: 50px;
      height: 50px;
      margin-right: 12px;
    }

    .title_wrap {
      flex: 1;

      .txt_container {
        width: 100%;
        display: -webkit-box;
        /* 使用弹性盒子布局 */
        -webkit-box-orient: vertical;
        /* 设置盒子内元素垂直排列 */
        -webkit-line-clamp: 2;
        /* 限制显示的行数 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;

        /* 超出部分显示省略号 */
        .prod_title {
          @include font12;
          color: #19191a;
          margin: 0;
          word-break: break-word;
        }
      }

      .prod_id {
        display: block;
        margin-top: 4px;
        @include font12;
        color: #89898c;
      }
    }
  }

  .qa_container {
    padding-top: 16px;

    .qa_wrap {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;

      &:last-child {
        margin: 0;
      }

      .dot {
        margin-top: 9px;
        width: 4px;
        height: 4px;
        background: #646466;
        border-radius: 50%;
      }

      .txt_container {
        max-width: 220px;
        margin-left: 6px;
        display: -webkit-box;
        /* 使用弹性盒子布局 */
        -webkit-box-orient: vertical;
        /* 设置盒子内元素垂直排列 */
        -webkit-line-clamp: 3;
        /* 限制显示的行数 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;

        /* 超出部分显示省略号 */
        .qa_text {
          @include font14;
          color: #19191a;
          margin: 0;

          &:hover {
            cursor: pointer;
            text-decoration: underline;
          }
        }
      }
    }
  }
  .video_container {
    padding-top: 16px;
    .divider {
      width: 100%;
      background-color: #e5e5e5;
      height: 1px;
    }
    .video_wrap {
      padding-top: 16px;
      .slide_item {
        height: 100%;
        display: flex;
        flex-direction: column;
        cursor: pointer;
        > .title_box {
          padding: 12px;
          border-radius: 0px 0px 8px 8px;
          border: 1px solid #e5e5e5;
          border-top: transparent;
          .title {
            @include font12;
            font-weight: 600;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
        }
        .img_box {
          width: 100%;
          height: 126px;
          overflow: hidden;
          position: relative;
          border-radius: 8px 8px 0px 0px;
          .img {
            display: block;
            max-width: 100%;
            width: 100%;
            position: absolute;
            top: 50%;
            left: 0;
            transform: translateY(-50%);
          }
          .time_box {
            position: absolute;
            bottom: 8px;
            right: 8px;
          }
        }

        &:hover {
          .title_box .title {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

.sg_form_wrap {
  display: flex;
  min-width: 40px;
  max-width: 272px;
  margin: 0 20px 16px 48px;
  padding: 8px 16px;
  background: #f5f5f9;
  border-radius: 0 20px 20px 20px;
  @include font13;
  color: #19191a;
  white-space: pre-line;
  overflow-wrap: break-word;

  // :deep() {
  //     a {
  //         color: #0070bc;
  //         word-wrap: break-word;
  //     }
  // }
  span {
    display: inline-block;
  }

  .input_wrap {
    flex: 1;

    .label {
      @include font12;
      color: #19191a;
      margin-bottom: 4px;
    }

    input {
      background-color: #fff;

      &:disabled {
        background-color: #f7f7f7;
      }
    }

    .suffix {
      position: absolute;
      right: 12px;
      top: 12px;
      width: 16px;
      height: 16px;
      font-size: 16px;
      cursor: pointer;

      &.error {
        color: #c00000;
      }

      &.success {
        color: #10a300;
      }
    }
  }
}

.sg_message_info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 16px;

  .message_chat {
    @include font12;
    color: #89898c;
    text-align: center;
    max-width: 100%;
  }

  .top {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .cs_avator {
      display: inline-block;
      width: 48px;
      height: 48px;
      flex-shrink: 0;
      background: #f5f5f9;
      border: 1.5px solid #eeeeee;
      border-radius: 36px;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: contain;
      position: relative;
    }

    .cs_online_dot {
      position: absolute;
      right: 3px;
      bottom: 0;
      width: 9px;
      height: 9px;
      border-radius: 50%;
      background-color: #10a300;
      border: 1.5px solid #eeeeee;
    }

    .cs_name {
      @include font12;
      color: #19191a;
      margin-top: 4px;
      font-weight: 600;
    }
  }
}

.robot_serve-enter-active,
.robot_serve-leave-active {
  transition: opacity 0.2s ease;
}

.robot_serve-enter-from,
.robot_serve-leave-to {
  opacity: 0;
}

.chat_robot_serve {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;

  .tag_wrap {
    width: 272px;
    border-radius: 8px;
    // border: 1px solid #e5e5e5;
    border-top: 2px solid #10a300;
    box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.15);

    .chat_robot_serve_item {
      padding: 12px 24px;
      border-bottom: 1px solid #e5e5e5;
      cursor: pointer;

      &:last-child {
        border: none;
      }

      &:hover {
        background: rgba(25, 25, 26, 0.04);
      }

      .msg_content {
        text-align: center;
        @include font14;
        font-weight: 600;
      }
    }
    .chat_form_input {
      margin: 0 24px;
      border-bottom: 1px solid #e5e5e5;
      position: relative;
      .input_el {
        width: 100%;
        border: none;
        padding: 12px 0;
        vertical-align: middle;
      }
      .status_icon {
        position: absolute;
        right: 0px;
        top: 26px;
        width: 16px;
        height: 16px;
        font-size: 16px;
        color: #10a300;
      }
      .label_el {
        @include font14;
        color: #707070;
        position: absolute;
        left: 0px;
        top: 12px;
        transition: all 0.3s ease;
        pointer-events: none;
        .iconfont {
          display: inline-block;
          font-size: 14px;
          color: #707070;
          margin-right: 4px;
          transition: all 0.3s ease;
          &.rotate {
            transform: rotate(180deg);
          }
        }
      }
      .dropdown_list {
        position: absolute;
        top: 34px;
        left: 0;
        width: 100%;
        background-color: #fff;
        border-radius: 4px;
        border: 1px solid #e5e5e5;
        box-shadow: 0px 3px 6px -2px rgba(0, 0, 0, 0.1);
        .dropdown_item {
          cursor: pointer;
          padding: 7px 12px;
          font-size: 12px;
          line-height: 20px;
          color: #19191a;
          &:hover {
            background: #f7f7f7;
          }
        }
      }
    }
    .validate_error {
      display: flex;
      align-items: center;
      gap: 4px;
      padding-left: 24px;
      margin-top: 4px;
      color: #c00000;
      @include font13;
    }
    .active_input {
      .input_el {
        height: 70px;
        padding-top: 36px;
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
      }
      .label_el {
        top: 12px;
        @include font12;
        .iconfont {
          font-size: 12px;
        }
      }
    }
    .submit_btn {
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 20px 24px;
      padding: 10px 24px;
      border-radius: 4px;
      border: 1px solid #19191a;
      .btn_content {
        display: flex;
        align-items: center;
      }
      span {
        font-size: 14px;
        line-height: 20px;
        color: #19191a;
      }
    }
    .freeze {
      border: 1px solid #10a300;
      .iconfont {
        color: #10a300;
        margin-right: 4px;
      }
      span {
        color: #10a300;
      }
    }
  }

  @keyframes fade-out {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }
}

.form_wrap {
  margin: 0 auto;
  margin-bottom: 16px;
  padding: 20px;
  width: 272px;
  font-size: 12px;
  box-sizing: border-box;
  border-radius: 8px;
  border-top: 2px solid #10a300;
  box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.15);

  .input_wrap {
    margin-bottom: 12px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .label {
      display: inline-block;
      margin-bottom: 4px;
    }

    .tel_code {
      width: 100%;
      display: flex;
      align-items: center;
      position: relative;

      &.tel_code_cn {
        .code {
          min-width: auto;
          flex: 0 0 50px;
          text-align: center;
          padding: 0 4px;

          .code_label {
            margin-right: 0;
          }
        }
      }

      .code {
        display: flex;
        align-items: center;
        background: $bgColor1;
        height: 42px;
        border: 1px solid $borderColor2;
        border-right: 0;
        border-radius: 2px 0 0 2px;
        cursor: pointer;
        // min-width: 96px;
        padding: 0 16px;
        flex-shrink: 0;

        .code_label {
          @include font13();
          color: $textColor1;
          flex: 1;
          margin-right: 12px;
        }
      }

      .tel {
        flex: 1 1 auto;
        border-radius: 0 3px 3px 0;
        user-select: text;

        &:focus {
          border-left: 1px solid #19191a;
        }
      }
    }
  }

  .form_submit {
    width: 100%;
    margin-top: 20px;
  }

  .prod_head {
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 16px;

    .prod_img {
      width: 50px;
      height: 50px;
      margin-right: 12px;
    }

    .title_wrap {
      flex: 1;

      .txt_container {
        width: 100%;
        display: -webkit-box;
        /* 使用弹性盒子布局 */
        -webkit-box-orient: vertical;
        /* 设置盒子内元素垂直排列 */
        -webkit-line-clamp: 2;
        /* 限制显示的行数 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;

        /* 超出部分显示省略号 */
        .prod_title {
          @include font12;
          color: #19191a;
          margin: 0;
          word-break: break-word;
        }
      }

      .prod_id {
        display: block;
        margin-top: 4px;
        @include font12;
        color: #89898c;
      }
    }
  }
}

.text_wrap {
  display: flex;
  min-width: 40px;
  max-width: 272px;
  margin: 0 20px 16px 48px;
  padding: 8px 16px;
  background: #f5f5f9;
  border-radius: 0 20px 20px 20px;
  @include font13;
  color: #19191a;
  white-space: pre-line;
  overflow-wrap: break-word;

  &.cs_text_break {
    word-break: break-all;
  }

  :deep(a) {
    color: #0070bc;
    word-wrap: break-word;
  }

  span {
    display: inline-block;
  }

  .input_wrap {
    flex: 1;

    .label {
      @include font12;
      color: #19191a;
      margin-bottom: 4px;
    }

    input {
      background-color: #fff;

      &:disabled {
        background-color: #f7f7f7;
      }
    }

    .suffix {
      position: absolute;
      right: 12px;
      top: 12px;
      width: 16px;
      height: 16px;
      font-size: 16px;
      cursor: pointer;

      &.error {
        color: #c00000;
      }

      &.success {
        color: #10a300;
      }
    }

    .sign_in {
      margin-top: 4px;
    }
  }
}

.message_sys_wrap {
  margin-bottom: 20px;
}

.message_sys_box {
  margin: 0 auto;
  max-width: 300px;
  text-align: center;

  .message_admin_time {
    text-align: center;
    color: #4b4b4d;
    @include font10;
    margin-bottom: 4px;
  }

  .message_admin_msg_box {
    display: inline-block;
    max-width: 100%;
    width: auto;
    background: #f5f5f9;
    min-height: 24px;
    border-radius: 24px;
    margin: 0 auto;
    position: relative;
    padding: 2px 12px 2px 28px;

    .iconfont_success {
      display: inline-block;
      width: 16px;
      height: 16px;
      flex-shrink: 0;
      // font-size: 18px;
      color: #fff;
      flex-shrink: 0;
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
    }

    .message_admin_msg {
      @include font10;
      color: #707070;
      text-align: left;
    }
  }

  @media (max-width: 960px) {
    width: 86.7%;
    max-width: none;
  }
}

.message_create_box {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;

  .message_chat {
    @include font12;
    color: #89898c;
    text-align: center;
    max-width: 100%;
  }

  .privacy {
    width: 300px;
    margin: 8px 0;
  }

  &.column {
    flex-direction: column;
    align-items: center;
  }

  .top {
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .cs_avator {
      display: inline-block;
      width: 48px;
      height: 48px;
      flex-shrink: 0;
      background: #f5f5f9;
      border: 1.5px solid #eeeeee;
      border-radius: 36px;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: contain;
      position: relative;
    }

    .cs_online_dot {
      position: absolute;
      right: 3px;
      bottom: 0;
      width: 9px;
      height: 9px;
      border-radius: 50%;
      background-color: #10a300;
      border: 1.5px solid #eeeeee;
    }

    .cs_name {
      @include font12;
      color: #19191a;
      margin-top: 4px;
      font-weight: 600;
    }
  }
}

.message_cs_wrap {
  display: flex;
  align-items: flex-end;
  padding-right: 20px;
  margin-bottom: 20px;
  position: relative;

  .cs_avator {
    display: inline-block;
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    background: #f5f5f9;
    // border: 1.5px solid #eeeeee;
    border-radius: 32px;
    margin-right: 8px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: contain;
  }

  .message_cs_box {
    .cs_content_wrap {
      position: relative;
      .cs_message_time {
        display: none;
        position: absolute;
        top: 50%;
        right: -50px;
        transform: translateY(-50%);
        @include font10;
        color: #4b4b4d;
      }
      &:hover {
        .cs_message_time {
          display: block;
        }
      }
    }
    .cs_name {
      @include font10;
      color: #4b4b4d;
      margin-bottom: 8px;
    }

    .cs_text {
      display: inline-block;
      min-width: 40px;
      max-width: 232px;
      padding: 8px 12px;
      // background: #f5f5f9;
      background: #f5f5f9;
      // border-radius: 0 20px 20px 20px;
      border-radius: 10px 10px 10px 0px;
      @include font14;
      color: #19191a;
      font-weight: 600;
      white-space: pre-line;
      overflow-wrap: break-word;

      &.cs_text_break {
        word-break: break-all;
      }

      a {
        color: #0070bc;
        word-wrap: break-word;
      }
    }

    .cs_text_loading {
      width: 60px;
      height: 20px;
      display: flex;
      justify-content: center;
      padding: 20px;
      background: #f5f5f9;
      border-radius: 0 20px 20px 20px;
    }

    .cs_img {
      display: inline-block;
      width: 160px;
      height: 160px;
      border-radius: 0 20px 20px 20px;
      background: #fff;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
      cursor: pointer;
    }

    .cs_video {
      video {
        max-width: 300px;
        max-height: 200px;
      }
    }

    .cs_file {
      background: #fff;
      box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
      border-radius: 0px 20px 20px 20px;
      border: 1px solid #f2f2f2;
      padding: 8px 12px 10px 12px;
      display: flex;
      align-items: center;
      cursor: pointer;
      width: 240px;
      text-align: left;

      .file_icon {
        display: inline-block;
        width: 48px;
        height: 48px;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .file_info {
        flex: 1 1 auto;

        .file_name {
          color: #232323;
          @include font13;
          margin-bottom: 6px;
          word-break: break-all;
        }

        .file_size {
          color: #999999;
          @include font12;
        }
      }
    }
  }

  // 添加连续消息的样式
  &.consecutive-message {
    margin-bottom: 4px; // 减小连续消息之间的间距

    .cs_avator_placeholder {
      margin-bottom: 0; // 移除占位符的底部间距
    }

    // .message_cs_box {
    //   .cs_name {
    //     display: none; // 隐藏连续消息中的名称
    //   }
    // }
  }
}

.message_user_wrap {
  display: flex;
  align-items: flex-start;
  padding-left: 5.4%;
  margin-bottom: 20px;
  flex-direction: row-reverse;
  text-align: right;
  &.consecutive-message {
    margin-bottom: 4px; // 减小连续消息之间的间距
  }

  .message_user_box {
    position: relative;
    .cs_message_time {
      display: none;
      position: absolute;
      top: 50%;
      right: calc(100% + 10px);
      transform: translateY(-50%);
      @include font10;
      color: #4b4b4d;
      white-space: nowrap;
      text-align: right;
    }
    &:hover {
      .cs_message_time {
        display: block;
      }
    }
    .cs_name {
      margin-top: 4px;
      @include font10;
      color: #4b4b4d;
      text-align: right;
      position: absolute;
      right: 0;
      bottom: -24px;
      white-space: nowrap;
    }
    .cs_text {
      display: inline-block;
      min-width: 40px;
      max-width: 232px;
      padding: 8px 12px;
      background: #10a300;
      // border-radius: 20px 0 20px 20px;
      border-radius: 10px 10px 0px 10px;
      text-align: left;
      @include font14;
      color: #fff;
      font-weight: 600;
      white-space: pre-line;
      overflow-wrap: break-word;

      .cs_text_break {
        word-break: break-all;
      }
      .iconfont {
        display: inline-block;
        width: 20px;
        height: 20px;
        font-size: 20px;
      }

      :deep(a) {
        color: #fff;
        word-wrap: break-word;
      }
    }

    .cs_img {
      display: inline-block;
      width: 160px;
      height: 160px;
      border-radius: 20px 0 20px 20px;
      background: #fff;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
      cursor: pointer;
    }

    .cs_video {
      video {
        max-width: 300px;
        max-height: 200px;
      }
    }

    .cs_file {
      background: #fff;
      box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
      border-radius: 20px 0 20px 20px;
      border: 1px solid #f2f2f2;
      padding: 8px 12px 10px 12px;
      display: flex;
      align-items: center;
      cursor: pointer;
      width: 240px;
      text-align: left;

      .file_icon {
        display: inline-block;
        width: 48px;
        height: 48px;
        flex-shrink: 0;
        margin-right: 8px;
      }

      .file_info {
        flex: 1 1 auto;

        .file_name {
          color: #232323;
          @include font13;
          margin-bottom: 6px;
          word-break: break-all;
        }

        .file_size {
          color: #999999;
          @include font12;
        }
      }
    }
  }
  .feedback_msg_wrap {
    &.good_msg {
      border: 1px solid #10a300;
    }
    &.bad_msg {
      border: 1px solid #c00000;
    }
    .msg_text {
      @include font14;
      color: #19191a;
    }
    .iconfont {
      width: 20px;
      height: 20px;
      font-size: 18px;
      color: #fff;
    }
  }
}

.message_wrap {
  &:last-child {
    margin-bottom: 0;
  }
}
