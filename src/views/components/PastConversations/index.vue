<template>
  <div class="past-conversations">
    <div class="header">
      <div class="close-btn" @click="handleBack">
        <span class="iconfont iconfont_back">&#xe702;</span>
      </div>
      <div class="title">{{ $c("conversationHistory") }}</div>
    </div>

    <div
      class="conversation-list"
      v-if="conversations.length > 0"
      ref="scrollContainer"
    >
      <div
        v-for="(item, index) in conversations"
        :key="index"
        class="conversation-item"
        @click="handleSelect(item)"
      >
        <div class="avatar">
          <img
            :src="
              item.messageType === 32
                ? agentAvatar
                : item.messageObject.avatar || defaultAvatar
            "
            alt="avatar"
          />
        </div>
        <div class="conversation-info">
          <div class="conversation-name-time">
            <div class="name" v-if="item.messageType === 32">
              {{ item.messageObject.name || $c("visitorUser") }}
            </div>
            <div class="name" v-else>
              {{ item.messageObject.name || $c("robot") }}
            </div>
            <div class="time">
              {{ utcToLocal(item.messageObject.messageTime) }}
            </div>
          </div>
          <div class="conversation-content">
            <div class="content-preview">{{ item.messageObject.msg }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="empty-state" v-else>
      <div class="empty-icon">
        <span class="iconfont">&#xe6a4;</span>
      </div>
      <div class="empty-text">{{ $c("noConversations") }}</div>
    </div>

    <div class="loading-state" v-if="loading">
      <GlobalLoading></GlobalLoading>
    </div>
  </div>
</template>

<script setup lang="ts">
export interface Root2 {
  groupId: any;
  groupIdStr: string;
  message: string;
  messageId: number;
  messageObject: MessageObject;
  messageType: number;
}

export interface MessageObject {
  avatar: string;
  code: number;
  fromUserId: string;
  groupId: string;
  messageId: string;
  messageReadStatus: boolean;
  messageTime: string;
  messageType: number;
  messageVisibilityType: number;
  msg: string;
  name: string;
  traceId: string;
  extraData?: any[];
}
import { ref, onMounted, inject, onUnmounted, watch, nextTick } from "vue";
import { getQueryString, utcToLocal } from "@/util/util";
import { $c } from "@/plugins/c-inject";
import EventBus from "@/util/eventBus";
import { post } from "@/util/request";
import GlobalLoading from "@/components/GlobalLoading.vue";

const emit = defineEmits(["backToChat", "selectConversation"]);
const $bus = inject<EventBus>("eventBus");
const siteCode = getQueryString("webSite") || "en";

const loading = ref(false);
const conversations = ref<any[]>([]);
const defaultAvatar = ref(
  "https://resource.fs.com/mall/generalImg/20241112101227kd5ex3.png"
);
const agentAvatar = ref(
  "https://resource.fs.com/mall/generalImg/202505201433369psnll.svg"
);
const currentPage = ref(1);
const hasMore = ref(true);
const pageSize = 10;
const scrollContainer = ref<HTMLElement | null>(null);

// 获取历史对话
const fetchConversations = async (isLoadMore = false) => {
  if (loading.value || (!isLoadMore && !hasMore.value)) return;

  loading.value = true;
  try {
    const res = (await post("/livechat/group/list", {
      current: currentPage.value,
      size: pageSize,
      appId: 1,
      search: localStorage.getItem("live_chat_userId") || 0,
      paragraph: localStorage.getItem("live_chat_group_id") || 0,
    })) as any;

    if (isLoadMore) {
      conversations.value = [...conversations.value, ...res.records];
    } else {
      conversations.value = res.records;
    }

    hasMore.value = res.records.length === pageSize;
    if (hasMore.value) {
      currentPage.value++;
    }
  } catch (error) {
    console.error("获取历史对话失败", error);
  } finally {
    loading.value = false;
  }
};

// 加载更多数据
const loadMore = () => {
  if (!loading.value && hasMore.value) {
    fetchConversations(true);
  }
};

// 滚动监听函数
const handleScroll = () => {
  if (!scrollContainer.value) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollContainer.value;
  // 当滚动到距离底部100px时，加载更多数据
  if (
    scrollHeight - scrollTop - clientHeight < 100 &&
    !loading.value &&
    hasMore.value
  ) {
    loadMore();
  }
};

// 绑定滚动事件
const bindScrollEvent = () => {
  nextTick(() => {
    scrollContainer.value = document.querySelector(".conversation-list");
    if (scrollContainer.value) {
      scrollContainer.value.addEventListener("scroll", handleScroll);
    }
  });
};

// 返回聊天界面
const handleBack = () => {
  // 返回按钮场景：返回到正在进行的聊天状态
  // 此时应该调用 fetchConversation 且 active 参数为 true
  emit("backToChat");
};

// 选择一个对话
const handleSelect = (conversation: any) => {
  // 场景一：选择历史对话时的逻辑优化
  // 获取当前存储的 groupIdStr（正在进行的会话ID）
  const currentGroupIdStr = localStorage.getItem('live_chat_group_id');
  const selectedGroupIdStr = conversation.groupIdStr;

  // 比较当前存储的 groupIdStr 与用户选中的会话 groupIdStr
  const isCurrentConversation = currentGroupIdStr === selectedGroupIdStr;

  // 根据比较结果决定 fetchConversation 的 active 参数：
  // - 如果相等：说明用户选择的是当前正在进行的会话，active 参数传 true
  // - 如果不相等：说明用户正在预览历史记录，active 参数传 false

  // 发送选择事件，包含是否为当前会话的标识
  // 父组件会根据 isCurrentConversation 来决定具体的处理逻辑
  emit("selectConversation", {
    ...conversation,
    isCurrentConversation
  });
};

onMounted(() => {
  fetchConversations();
});

// 监听 conversations 变化，确保列表渲染完成后再绑定滚动事件
watch(
  conversations,
  (newVal) => {
    if (newVal.length > 0) {
      bindScrollEvent();
    }
  },
  { immediate: false }
);

onUnmounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener("scroll", handleScroll);
  }
});
</script>

<style scoped lang="scss">
.past-conversations {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;

  .header {
    display: flex;
    align-items: center;
    // padding: 17px 16px 17px 20px;
    padding: 16px 0 17px;
    margin: 0 16px 0 20px;
    border-bottom: 1px solid #eee;

    .title {
      @include font18;
      font-weight: 600;
    }

    .close-btn {
      cursor: pointer;
      margin-right: 8px;
      .iconfont_back {
        font-size: 20px;
      }
    }
  }

  .conversation-list {
    flex: 1;
    overflow-y: auto;
    padding: 0px 16px;
    &::-webkit-scrollbar {
      width: 4px;
      background: #f7f7f7;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #9fa2b4;
    }

    &::-webkit-scrollbar-track {
      border-radius: 10px;
      background: #ffffff;
    }
    .conversation-item {
      padding: 8px 0;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        background-color: #f9f9f9;
      }

      .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .conversation-info {
        flex: 1;
        .conversation-name-time {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .name {
            @include font14;
            font-weight: 600;
            color: #19191a;
          }
          .time {
            @include font12;
            color: #707070;
          }
        }
        .conversation-content {
          flex: 1;
          .content-preview {
            width: 280px;
            min-height: 20px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            @include font14;
            color: #707070;
          }
        }
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .empty-icon {
      font-size: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: 14px;
      color: #999;
    }
  }

  .loading-state {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #333;
      animation: spin 1s linear infinite;
    }
  }

  .load-more {
    padding: 12px;
    text-align: center;
    color: #999;
    font-size: 12px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
