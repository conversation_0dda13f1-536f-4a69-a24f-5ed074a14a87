<template>
    <section class="mobile_live_chat_footer">
      <div class="tag_wrap" v-if="showEndBtn">
        <div class="close_chat tag" @click="handleEndChat">
          <img src="@/assets/svg/app/app_icon11.svg" alt="">
          <span>End Chat</span>
        </div>
        <!-- <div class="prod_tag tag" @click="handleOpenProduct">
          <img
            src="@/assets/svg/app/app_icon1.svg"
            alt=""
          >
          <span>Product</span>
        </div>
        <div class="policy_tag tag">
          <img
            src="@/assets/svg/app/app_icon2.svg"
            alt=""
          >
          <span>Policy Info</span>
        </div> -->
      </div>
      <div class="main_wrap">
        <textarea
          type="text"
          class="mobile_input"
          enterkeyhint="enter"
          :placeholder="$c('placeholder')"
          @input.stop="handleInput"
          v-model="inputValue"
          @focus="handleInputFocus"
        />
        <div class="circle_btn" @click="handleOpenCamera">
          <img
            src="@/assets/svg/app/app_icon3.svg"
            alt=""
          >
        </div>
        <section>
          <div class="circle_btn" @click="handleOpenDropDown" v-if="!inputValue && !openDropDown">
            <img
              src="@/assets/svg/app/app_icon4.svg"
              alt=""
            >
          </div>
          <div class="circle_btn close" v-else-if="!inputValue && openDropDown" @click="handleOpenDropDown">
            <img src="@/assets/svg/app/app_icon10.svg" alt="">
          </div>
          <div class="circle_btn send" v-else @click="handleEnter">
            <img src="@/assets/svg/app/app_icon9.svg" alt="">
          </div>
        </section>
      </div>
      <DropDown>
        <section v-show="!footerDisabled && openDropDown">
          <div class="options_wrap">
              <div class="item_option" v-for="item in optionList">
                   <div class="icon_wrap" @click="optionsClick(item.id)">
                    <img
                      :src="item.icon"
                      alt=""
                    >
                    <input type="file" :accept="acceptFile.join(',')" v-if="item.id === 2" @change.stop="handleFileChange">
                  </div>
                  <span>{{ item.text }}</span>
              </div>
          </div>
        </section>
    </DropDown>
    <div class="mobile_footer_disabled" v-if="footerDisabled"></div>
    </section>
</template>

<script setup lang="ts">
import { inject, ref, onMounted } from 'vue';
import { isAndroid } from '@/util/util';
import EventBus from '@/util/eventBus';

import DropDown from '@/components/DropDown.vue';
import AppIcon5 from '@/assets/svg/app/app_icon5.svg';
import AppIcon6 from '@/assets/svg/app/app_icon6.svg';
import AppIcon7 from '@/assets/svg/app/app_icon7.svg';
import AppIcon8 from '@/assets/svg/app/app_icon8.svg';

type MobileFooterProps = {
  acceptFile: string[];
  footerDisabled: boolean;
}
const $bus = inject<EventBus>('eventBus')
const emit = defineEmits(['mobileFooterInput', 'mobileFooterSend', 'mobileFileChange', 'mobileFilesSend', 'mobileInputFocus'])
const props = withDefaults(defineProps<MobileFooterProps>(), {
  acceptFile: () => [],
  footerDisabled: false,
})
const showEndBtn = ref(false)
const isAndroidBool = isAndroid()
const optionList = [
  {
    icon: AppIcon5,
    text: 'Gallery',
    id: 1
  },
  {
    icon: AppIcon6,
    text: 'File',
    id: 2
  },
  // {
  //   icon: AppIcon7,
  //   text: 'Zoom',
  //   id: 3
  // },
  // {
  //   icon: AppIcon8,
  //   text: 'Order',
  //   id: 4
  // }
]
const openDropDown = ref(false)
const inputValue = ref('')

onMounted(() => {
  const endBtnStatus = window.localStorage.getItem('show_end_btn')
  showEndBtn.value = endBtnStatus === 'true'
  $bus.on('showEndBtn', (val: boolean) => {
    showEndBtn.value = val
  })
})

const handleOpenCamera = () => {
  if (isAndroidBool) {
    window.chatJsInterface.openCamera()
  } else {
    window.livechat.chatJsInterface('openCamera', '')
  }
}

const optionsClick = (id: number) => {
  switch (id) {
    case 1:
      handleGalleryChange();
      break;
    case 4:
      handleOpenOrder()
    default:
      break;
  }
}
const handleOpenProduct = () => {
  if (isAndroidBool) {
    window.chatJsInterface.chooseProduct()
  } else {
    window.livechat.chatJsInterface("chooseProduct", "")
  }
}
function handleOpenOrder() {
  if (isAndroidBool) {
    window.chatJsInterface.chooseOrder()
  } else {
    window.livechat.chatJsInterface("chooseOrder", "")
  }
}
/**
 * 
 * @description 输入框聚焦
 */
function handleInputFocus () {
  $bus.emit('mobileInputFocus')
}

const handleEndChat = () => {
  // emit('mobileEndChat')
  $bus.emit('mobileEndChat')
}

function handleGalleryChange() {
  if (isAndroidBool) {
    window.chatJsInterface.openGallery()
  } else {
    window.livechat.chatJsInterface("openGallery", "")
  }
}
function handleFileChange(e: Event) {
  emit('mobileFileChange', e)
}
const handleOpenDropDown = () => {
  openDropDown.value = !openDropDown.value
}
const handleInput = (e: Event) => {
  emit('mobileFooterInput', e)
}

const handleEnter = () => {
  inputValue.value = ''
  emit('mobileFooterSend')
}
const getFilesResults = async (results: any) => {
  if (!isAndroidBool) {
    results = JSON.parse(results)
  }
  emit('mobileFilesSend', results)
}
onMounted(() => {
    window.getFilesResults = getFilesResults
}) 
</script>

<style scoped lang="scss">
@import './index.scss';
</style>