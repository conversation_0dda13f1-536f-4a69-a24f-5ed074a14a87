.mobile_live_chat_footer {
  position: relative;
  display: flex;
  flex-direction: column;
  // gap: calc(20 / $rem);
  width: 100%;
  // height:calc( 184 / $rem);
  flex-shrink: 0;
  padding: calc(20 / $rem) calc(32 / $rem);
  // padding-bottom: constant(safe-area-inset-bottom);
  // padding-bottom: env(safe-area-inset-bottom);
  background-color: #F6F6F6;
  color: #232323;

  .mobile_footer_disabled {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.6;
    background: #fff;
    cursor: not-allowed;
  }

  .tag_wrap {
    display: flex;
    margin-bottom: calc(20 / $rem);
    gap: calc(16 / $rem);
    align-items: center;

    .tag {
      padding: calc(11 / $rem) calc(24 / $rem);
      border-radius: calc(28 / $rem);
      background-color: #fff;
      display: flex;
      align-items: center;
      gap: calc(10 / $rem);

      img {
        width: calc(26 / $rem);
        height: calc(26 / $rem);
      }

      span {
        font-size: calc(26 / $rem);
        line-height: calc(34 / $rem);
      }
    }
  }

  .main_wrap {
    display: flex;
    align-items: center;
    gap: calc(16 / $rem);

    // margin-top: calc(20/ $rem);
    .mobile_input {
      width: calc(510 / $rem);
      height: calc(72 / $rem);
      border-radius: calc(36 / $rem);
      background-color: #fff;
      border: none;
    }

    .circle_btn {
      display: flex;
      width: calc(72 / $rem);
      height: calc(72 / $rem);
      border-radius: 50%;
      background-color: #fff;
      align-items: center;
      justify-content: center;

      img {
        width: calc(30 / $rem);
        height: calc(30 / $rem);
      }

      &.send {
        background-color: #C00000;
      }

      &.close {
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  :deep(.options_wrap) {
    display: flex;
    align-items: center;
    gap: calc(74 / $rem);
    padding: calc(20 / $rem) calc(32 / $rem) calc(48 / $rem);

    .item_option {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: calc(16 / $rem);

      .icon_wrap {
        position: relative;
        display: flex;
        width: calc(100 / $rem);
        height: calc(100 / $rem);
        justify-content: center;
        align-items: center;
        border-radius: calc(10 / $rem);
        background-color: #fff;

        img {
          width: calc(40 / $rem);
          height: calc(40 / $rem);
        }

        input {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: inline-block;
          opacity: 0;
        }
      }

      span {
        font-size: calc(24 / $rem);
        line-height: calc(32 / $rem);
        color: #5B5F6B;
      }
    }
  }
}