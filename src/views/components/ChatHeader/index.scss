.live_chat_header {
  width: 100%;
  height: 60px;
  flex-shrink: 0;
  // box-shadow: 0px 1px 0px 0px #f2f2f2;
  padding: 0 16px 0 20px;
  z-index: auto;
  position: relative;
  .header_wrap {
    border-bottom: 1px solid #dee0e3;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
  }
  .header_cs_status {
    font-weight: 600;
    text-align: left;
    color: #19191a;
    @include font18;
    padding-left: 16px;
    position: relative;
    user-select: none;
    &::before {
      content: " ";
      display: inline-block;
      width: 9px;
      height: 9px;
      border-radius: 50%;
      background: #10a300;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translate3d(0, -50%, 0);
    }

    &.header_cs_status_2 {
      &::before {
        background: #e30505;
      }
    }

    &.header_cs_status_0 {
      &::before {
        background: #4c4948;
      }
    }
  }

  .header_right {
    display: flex;
    justify-content: center;
  }

  .icon_wrap {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #707070;
    cursor: pointer;
    &:first-child {
      margin-right: 8px;
    }
    &:hover {
      color: #19191a;
      background-color: rgba($color: #19191a, $alpha: 0.04);
      border-radius: 3px;
    }
  }
  .iconfont_minimize {
    width: 20px;
    height: 20px;
    font-size: 20px;
    transition: all 0.3s;
  }

  .iconfont_close {
    width: 20px;
    height: 20px;
    font-size: 20px;
    transition: all 0.3s;
  }
}
.title_list {
  padding: 4px;
  .title_item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    cursor: pointer;
    &:hover {
      background: #f6f6f8;
    }
    .iconfont {
      font-size: 18px;
      width: 20px;
      height: 20px;
      margin-right: 4px;
    }
    .title_item_des {
      @include font13;
      flex: 1;
      white-space: nowrap;
    }
  }
}
