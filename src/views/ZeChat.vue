<template>

</template>

<script setup lang="ts">
import { post } from '@/util/request';
import { getAllQueryStrings, isAndroid } from '@/util/util';
import { useScript } from '@unhead/vue';

const isAndroidBool = isAndroid()
console.log('urlSearchParams', getAllQueryStrings());
const { clientUserInfo: clientUserInfoString, token, webSite, countryName } = getAllQueryStrings();
const clientUserInfo = JSON.parse(clientUserInfoString || "{}");
console.log('clientUserInfo', clientUserInfo);
console.log('token', token);

const { zE } = useScript({
  src: 'https://static.zdassets.com/ekr/snippet.js?key=a35bc69f-c6f6-4cde-b7f3-a3255ec4d9f9',
  type: 'text/javascript',
  id: 'ze-snippet',
  defer: true,
}, {
  use: () => {
    return { zE: window.zE };
  }
})
if (token) {
  authZendeskChat();
}
console.log("zendesk get customer_email_address:", clientUserInfo?.email)
zE("messenger:set", "conversationFields", [{ id: "28194227804441", value: clientUserInfo?.email || "" }])
// customer number field
console.log("zendesk get customer_number:", clientUserInfo?.customer_number)
zE("messenger:set", "conversationFields", [{ id: "35684258978073", value: clientUserInfo?.customer_number || "" }])
// webSite field
console.log("zendesk get webSite:", webSite)
zE("messenger:set", "conversationFields", [{ id: "35684241634073", value: webSite }])
// country field
console.log("zendesk get country_name: ", countryName)
zE("messenger:set", "conversationFields", [{ id: "28965480548121", value: countryName }])
zE("messenger:on", "unreadMessages", function (count: number) {
  console.log(`You have ${count} unread message(s).`)
})

console.log('wait for 300ms to open zendesk chat');
zE("messenger", "open")


zE("messenger:on", "close", function () {
  if (isAndroidBool) {
    window.chatJsInterface.closeWebPage()
  } else {
    window.livechat.chatJsInterface("closeWebPage", "")
  }
})
async function authZendeskChat() {
  const res = await post('/collector/api/zendesk/generateJwtToken', {}, {
    headers: {
      'Authorization': `Bearer ${token}`
    },
  }) as any;
  if (res.token) {
    const signedJwt = res.data.token
    zE("messenger", "loginUser", function (callback: (jwt: string) => void) {
      callback(signedJwt)
      console.log("loginUser with token", signedJwt)
    })
  }
}

</script>

<style scoped lang="scss"></style>