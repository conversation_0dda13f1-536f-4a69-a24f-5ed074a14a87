import { createApp } from 'vue'
import { createHead } from "@unhead/vue";
import '@/assets/scss/reset.scss'
import './style.css'
import App from './App.vue'
import i18n from '@/lang'
import '@/assets/iconfont/iconfont.css'
import i18nC from './plugins/c-inject'
import '@/util/flexible'
import EventBus from './util/eventBus' // vue3移除了new vue()挂载事件总线，可以安装mitt第三方库
import clickOutside from "./plugins/clickOutside";

const eventBus = new EventBus()
const app = createApp(App)
// 全局注册指令
app.directive('click-outside', clickOutside);
const head = createHead();
app.use(head);
/**
 * 通过provide将eventBus挂载到全局,方便在单文件中使用
 * 如果使用globalProperties挂载到全局，引用的时候需要使用getCurrentInstance()?.proxy.eventBus
 */
app.provide('eventBus', eventBus)

app.use(i18n)
app.use(i18nC)
app.mount('#app')

