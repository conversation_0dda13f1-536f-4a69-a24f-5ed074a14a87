
import { createI18n } from 'vue-i18n'
import en from './en'
import cn from './cn'
import de from './de'
import es from './es'
import fr from './fr'
import it from './it'
import jp from './jp'
import ru from './ru'
import nl from './nl'
const messages = {
  en,
  sg: en,
  uk: en,
  'de-en': en,
  mx: es,
  cn,
  de,
  es,
  fr,
  it,
  jp,
  ru,
  nl,
}
const i18n = createI18n({
  legacy: false,
  locale: 'en', // 默认语言
  messages
})

export default i18n