export default {
  popupTitle: "Chattez avec nous",
  placeholder: "Entrez le texte ici...",
  fileType: "Fichier local / Photo",
  chatNow: "Chat Maintenant",
  endChat: "Term<PERSON> le Chat ？",
  closeTips: "Voulez-vous vraiment fermer le chat ?",
  closeBtn: "Ferm<PERSON>",
  feedbackTitle: "Veuillez évaluer votre conversation",
  good: "<PERSON><PERSON>",
  bad: "<PERSON>uvaise",
  rateTip: "Vous avez évalué",
  hasVoted: "Merci pour votre vote !",
  commentTitle: "Avez-vous des commentaires ?",
  ratePlaceholder: "Entrez vos commentaires...",
  submit: "Envoyer",
  read: "<PERSON>",
  unread: "Pas lu",
  emailTranscript: "Envoyer cette transcription par e-mail",
  mute: "Muet",
  unmute: "<PERSON>r le son",
  emailTip:
    "Veuillez entrer l'adresse e-mail à laquelle vous souhaitez envoyer la transcription de cette conversation.",
  sendBtn: "Envoyer un e-mail",
  sendStatus: "Email envoyé",
  sendInfoStart:
    "La transcription de cette conversation a été envoyée par e-mail à ",
  sendInfoEnd:
    ". Si vous ne recevez pas d'e-mail dans les minutes suivantes, veuillez vérifier votre dossier d'e-mail indésirable.",
  backChat: "Retour à la conversation",
  vaildInfo: "Désolé, il semble que ce ne soit pas une adresse e-mail valide.",
  message: "Message",
  messages: "Messages",
  downloadApp: "Télécharger l'Application FS",
  more3Files: "Télécharger jusqu'à 3 fichiers",
  fileSize5M: "Taille maximale du fichier : 5M",
  fileSize20M: "Taille maximale du fichier : 20M",
  invalidFileType: "Type de fichier invalide",
  robot: "Assistant Virtuel FS",
  advisor: "Conseiller Fs",
  typing: "L'agent tape...",
  newFeedback: {
    title: "Évaluez la conversation",
    comment: "commentaire",
    update: "Mise à jour",
  },
  userForm: {
    name: "Nom",
    email: "Adresse E-mail",
    selectAgent: "Sélectionnez un agent",
    selectOption: ["Support du Produit", "Support Technique"],
    saved: "Enregistré",
  },
  placeholder2: "Écrire une réponse...",
  viewPastConversations: "Voir les conversations précédentes",
  conversationHistory: "Historique des conversations",
  videoTip: {
    title: "Regarder la Vidéo",
    text: "Regarder la Vidéo dans une Nouvelle Fenêtre ?",
    btn: "Commencer Maintenant",
  },
  visitorUser: "Utilisateur Visiteur",
  privacyPolicy: {
    acceptText:
      'En acceptant, vous autorisez expressément FS.com, Inc., ses affiliés et ses prestataires de services à enregistrer, stocker, partager et analyser le contenu de vos conversations de chat. Pour en savoir plus, veuillez consulter notre <a href="https://www.fs.com/policies/privacy_notice.html" target="_blank">Politique de confidentialité et notre Avis de collecte</a>.',
    rejectText:
      "D’accord. Vous pouvez consulter la politique à tout moment et consentir à continuer.",
    acceptBtn: "Oui, j'accepte.",
    rejectBtn: "Non, pas maintenant.",
  },
};
