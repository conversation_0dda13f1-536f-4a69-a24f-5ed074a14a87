export default {
  popupTitle: "Chat with Us",
  placeholder: "Enter text here...",
  fileType: "Local file/Picture",
  chatNow: "Chat Now",
  endChat: "End Chat?",
  closeTips: "Do you really want to close the chat?",
  closeBtn: "Close",
  feedbackTitle: "Please rate your conversation",
  good: "Good",
  bad: "Bad",
  rateTip: "You have rated",
  hasVoted: "Thank you for your vote!",
  commentTitle: "Do you have any feedback?",
  ratePlaceholder: "Type your comments...",
  submit: "Submit",
  read: "Read",
  unread: "Unread",
  emailTranscript: "Email this transcript",
  mute: "Mute",
  unmute: "Unmute",
  emailTip:
    "Please enter the email address where you'd like to send the transcript of this conversation",
  sendBtn: "Send email",
  sendStatus: "Email sent",
  sendInfoStart: "A transcript of this conversation has been emailed to ",
  sendInfoEnd:
    ". If you don't receive an email within a few minutes, please check your Spam folder.",
  backChat: "Go back to conversation",
  vaildInfo: "Sorry, it doesn't seem to be a valid email address.",
  message: "Message",
  messages: "Messages",
  downloadApp: "Download FS App",
  more3Files: "Upload up to 3 files",
  fileSize5M: "Maximum file size 5M.",
  fileSize20M: "Maximum file size 20M.",
  invalidFileType: "Invalid file type.",
  robot: "Virtual Assistant",
  advisor: "Fs Advisor",
  formLabel: {
    name: "Name (Optional)",
    email: "Email address",
    tel: "Phone Number",
    help: "How can we help?",
    Optional: "Optional",
  },
  formError: {
    name: "",
    email: {
      error1: "This field is required.",
      error2: "Please enter a valid email address.",
    },
    tel: "",
    help: "This field is required.",
  },
  typing: "Agent is typing...",
  newFeedback: {
    title: "Rate the conversation",
    comment: "Comment",
    update: "Update",
  },
  userForm: {
    name: "Name",
    email: "Email address",
    selectAgent: "Select agent",
    selectOption: ["Product purchase", "Technical support"],
    saved: "Saved",
  },
  placeholder2: "Write a reply...",
  viewPastConversations: "View past conversations",
  conversationHistory: "Conversation history",
  videoTip: {
    title: "Watch Video",
    text: "Watch the Video in New Window?",
    btn: "Start Now",
  },
  visitorUser: "Visitor User",
  privacyPolicy: {
    acceptText: "By choosing to accept, you expressly agree that FS.com, Inc., its affiliates, and service providers may record, store, share, and analyze the content of your chat conversations. For more information please review our <a href=\"https://www.fs.com/policies/privacy_notice.html\" target=\"_blank\">Privacy Policy and Notice at Collection</a>.",
    rejectText: "That's okay. You can view the policy any time and consent to continue",
    acceptBtn: "Yes, I Accept.",
    rejectBtn: "No, Not now."
  },
};
