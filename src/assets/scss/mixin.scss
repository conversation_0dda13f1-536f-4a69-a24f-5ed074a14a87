$textColor1: #19191a;
$textColor2: #646466;
$textColor3: #89898c;
$textColor4: #c00000;
$textColor5: #4b4b4d;
$textColor6: #0060bf;
$textColor7: #ffffff;
$textColor8: #035fbf;
$textColor9: #999999;
$textColor10: #936d1d;

$bgColor1: #f7f7f7;
$bgColor2: #e5e5e5;
$bgColor3: #ffffff;
$bgColor4: #f4f4f4;
$bgColor5: #c00000;
$bgColor6: #eeeeee;
$bgColor7: #fafafb;

$btnBgColor1: #c00000;
$btnBgColor2: #9b0912;
$btnBgColor3: #4b4b4d;
$btnBgColor4: #f2f2f2;
$btnBgColor4: #f2f2f2;
$btnBgColor5: #e5e5e5;
$btnBgColor6: #828282;
$btnBgColor7: #eb9898;

$borderColor1: #c00000;
$borderColor2: #e5e5e5;
$borderColor3: #4b4b4d;
$borderColor4: #9b0912;
$borderColor5: #eeeeee;
$borderColor6: #cccccc;
$borderColor7: #dedede;
$borderColor8: #eaeaea;
$borderColor9: #4c4948;
$borderColor10: #89898c;
$borderColor11: #e8e9eb;

$popupWidth1: 480px;
$popupWidth2: 680px;
$popupWidth3: 750px;
$popupWidth4: 880px;
// 新设计稿色值
// 旧               新
// 232323    对应   19191a
// 4c4948    对应   4b4b4d
// 616265    对应   646466
// 8d8d8f    对应    89898c
// 0070bc    对应    0060bf

$accountMaxWidth: 1420px;

@mixin font10 {
    font-size: 10px;
    line-height: 20px;
}
@mixin font12 {
    font-size: 12px;
    line-height: 18px;
}

@mixin font13 {
    font-size: 13px;
    line-height: 20px;
}

@mixin font14 {
    font-size: 14px;
    line-height: 22px;
}

@mixin font16 {
    font-size: 16px;
    line-height: 24px;
}

@mixin font18 {
    font-size: 18px;
    line-height: 26px;
}

@mixin font20 {
    font-size: 20px;
    line-height: 30px;
}

@mixin font24 {
    font-size: 24px;
    line-height: 32px;
}

@mixin font26 {
    font-size: 26px;
    line-height: 34px;
}

@mixin font28 {
    font-size: 28px;
    line-height: 34px;
}

@mixin font30 {
    font-size: 30px;
    line-height: 36px;
}

@mixin font32 {
    font-size: 32px;
    line-height: 40px;
}

@mixin font34 {
    font-size: 34px;
    line-height: 42px;
}

@mixin font36 {
    font-size: 36px;
    line-height: 44px;
}

@mixin font40 {
    font-size: 40px;
    line-height: 48px;
}

@mixin bgcover($src) {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    background-image: url($src);
}

@mixin bgcontain($src) {
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    background-image: url($src);
}

@mixin pc_tip {
    padding: 20px;
}

@mixin m_tip {
    width: 100%;
    padding: 40px 20px 30px 20px;
}

@mixin rowLine {
    position: absolute;
    display: block;
    content: "";
    width: 100%;
    height: 3px;
    border-radius: 3px;
    background-color: #c00000;
    left: 0;
    bottom: 0;
    opacity: 0;
    transition: all 0.2s;
}

@mixin video_hover {
    background-color: rgba(25, 25, 26, 0.8);
}

@mixin sourceSansPro {
    //   font-family: "Source Sans Pro","Open Sans";
    font-weight: 600;
}

@mixin width1200 {
    width: 84vw;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1200px) {
        width: 94vw;
    }
}

@mixin errorInput {
    border: 1px solid $btnBgColor5;
}

@mixin mediaIpad {
    @media (max-width: 1024px) {
        @content;
    }
}

@mixin mediaM {
    @media (max-width: 768px) {
        @content;
    }
}

@mixin chooseLine {
    position: absolute;
    display: block;
    content: "";
    width: 3px;
    height: 100%;
    background-color: #c00000;
    left: 0;
    bottom: 0;
    opacity: 0;
    -webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
    transition: all 0.2s;
}

@mixin fsHoverUnderline {
    display: inline;
    padding-bottom: 4px;
    background-size: 100% 0;
    background-position: left bottom;

    &:hover {
        text-decoration: none;
        background: linear-gradient($bgColor5, $bgColor5) repeat-x left bottom;
        background-size: 100% 1.5px;
    }
}

@mixin underline($col: currentColor, $width: 0) {
    position: relative;
    cursor: pointer;

    &::before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: $width;
        height: 1px;
        background-color: $col;
    }

    &:hover {
        &::before {
            width: 100%;
        }
    }
}

@mixin scrollY {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

@mixin txt-hid {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

@mixin txt-more-hid($line: 2) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: $line;
}

@mixin fsUnderline {
    display: inline;
    text-decoration: none;
    padding-bottom: 4px;
    background: linear-gradient($bgColor5, $bgColor5) repeat-x left bottom;
    background-size: 100% 1.5px;
}

@mixin accountWidthBox($width: $accountMaxWidth) {
    width: $width;
    max-width: 84vw;
    margin: 0 auto;
    @include mediaIpad {
        max-width: 100%;
    }
}

@mixin accountBox {
    padding: 0 0 60px 0;
    @include mediaIpad {
        padding: 24px 24px 40px;
        .page-left {
            display: none;
        }
    }
    @include mediaM {
        padding: 20px 16px 40px;
    }
}
@mixin emptyShadow {
    border-radius: 12px;
    box-shadow: 0 2px 8px 0 rgb(0 0 0 / 10%);
}

@mixin blackLink {
    color: #19191a;
    text-decoration: underline;
    cursor: pointer;
    &:hover {
        color: #646466;
    }
}
@mixin formUploadStyle {
    :deep(.upload-file-wrap){
        &:hover {
            .file-btn {
                background: #f7f7f7;
            }
        }
        .fs-popover {
            margin-left: 12px;
        }
        .file-btn {
            padding: 10px 24px;
            background: #fafafb;
            border-radius: 3px;
            border: 1px solid #e5e5e5;
        }
        .iconfont-upload {
            margin-right: 8px;
        }
        .file-info {
            display: flex;
            align-items: center;
            .info-txt {
                @include font14;
                color: $textColor1;
            }
            .upload-tip-wrap {
                width: 0;
                display: flex;
                align-items: center;
            }
        }
        .file-box {
            margin-top: 12px;
            .file-item {
                height: initial;
                padding: 8px 24px;
                @include font14;
                color: $textColor1;
                background: #f2f2f2;
            }
            .iconfont-delete {
                font-size: 16px;
                color: #89898c;
                margin-left: 12px;
                line-height: 20px;
            }
        }
    }
}

@mixin blueLink {
    color: #0060bf;
    cursor: pointer;
    &:hover {
        text-decoration: underline;
    }
}
