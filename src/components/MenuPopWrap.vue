<template>
  <div
    class="menu_pop_wrap"
    ref="menuPopBox"
    :style="{ top: site_code !== 'cn' ? '-176px' : '-123px' }"
  >
    <div class="arrow"></div>
    <div class="content_wrap">
      <div class="option_wrap">
        <div
          class="option"
          v-for="item in htmlList"
          :key="item.icon"
          @click="handleClick(item.type)"
        >
          <div
            v-if="item.type === 1 || (item.type === 2 && site_code !== 'cn')"
            class="item"
          >
            <i
              class="iconfont icon"
              v-html="item.icon"
            ></i>
            <div class="des">
              {{ item.des }}
            </div>
          </div>
          <div
            v-else-if="item.type === 3"
            class="item"
          >
            <i
              class="iconfont icon"
              v-html="!muteStatus ? item.icon : '&#xf276;'"
            ></i>
            <div class="des">
              {{ !muteStatus ? item.des : $c("mute") }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { $c } from '@/plugins/c-inject';
import { dataLayerToParent } from '@/util/util';
import { onBeforeUnmount, onMounted, ref} from 'vue'

type MenuPopWrapProps = {
  site_code?: string;
}
const props = withDefaults(defineProps<MenuPopWrapProps>(), {
  site_code: 'en',
})

const menuPopBox = ref(null)
const emit = defineEmits(['showAddress', 'downloadApp', 'hide', 'changeMute'])
const htmlList = ref([
  {
    type: 1,
    icon: "&#xf277;",
    des: $c("emailTranscript"),
  },
  {
    type: 2,
    icon: "&#xf061;",
    des: $c("downloadApp"),
  },
  {
    type: 3,
    icon: "&#xf275;",
    des: $c("unmute"),
  },
])
const muteStatus = ref(false)

const handleClick = (type: number) => {
  switch (type) {
    case 1:
      emit('showAddress')
      break;
    case 2:
      dataLayerToParent({
        eventLabel: "chat with app"
      })
      window.open(`https://www.fs.com/appdownload.html`)
      break;
    case 3:
      muteStatus.value = !muteStatus.value
      emit('changeMute', muteStatus.value)
      dataLayerToParent({
        eventLabel: `${!muteStatus.value ? 'Unmute' : 'Mute'}`
      })
      break;
    default:
      break;
  }
  emit('hide')
}
onMounted(() => {
  document.addEventListener('click', (e) => clickElementHide(e))
})

onBeforeUnmount(() => {
  document.removeEventListener('click', (e) => clickElementHide(e))
})
const clickElementHide = (e: Event) => {
  const target = e.target as HTMLElement
  if (menuPopBox.value) {
    if (!menuPopBox.value.contains(target)) {
      emit('hide')
    }
  }
}
</script>

<style scoped lang="scss">
.option_wrap {
  display: flex;
  flex-direction: column;

  .option {
     &:last-child {
        .item {
           border: none;
        }
      }
    .item {
      flex: 1;
      padding: 16px 0;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border-bottom: 1px solid #e5e5e5;
      cursor: pointer;
      .icon {
        width: 20px;
        height: 20px;
        margin-right: 4px;
        font-size: 20px;
        color: #4b4b4d;
      }

      .des {
        @include font13;
        color: #19191a;
      }
    }
  }
}</style>