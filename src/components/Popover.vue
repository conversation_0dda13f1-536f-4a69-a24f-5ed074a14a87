<template>
  <div
    class="popover-container"
    ref="container"
  >
    <div
      ref="triggerEl"
      @click="props.trigger === 'click' && togglePopover()"
      @mouseenter="props.trigger === 'hover' && showPopover()"
      @mouseleave="props.trigger === 'hover' && hidePopover()"
    >
      <slot name="trigger"></slot>
    </div>
    <div
      v-show="visible"
      class="popover"
      :class="[`popover--${props.placement}`]"
      ref="popover"
    >
      <div class="popover__arrow" :class="[`popover__arrow--${props.placement}`]"></div>
      <div class="popover__content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';

type Placement = 
  | 'top' | 'top-left' | 'top-right' 
  | 'bottom' | 'bottom-left' | 'bottom-right' 
  | 'left' | 'left-top' | 'left-bottom' 
  | 'right' | 'right-top' | 'right-bottom';

const props = defineProps({
  trigger: {
    type: String,
    default: 'click', // 'click' 或 'hover'
    validator: (value: string) => ['click', 'hover'].includes(value)
  },
  placement: {
    type: String,
    default: 'bottom', // 位置选项
    validator: (value: string) => [
      'top', 'top-left', 'top-right',
      'bottom', 'bottom-left', 'bottom-right',
      'left', 'left-top', 'left-bottom',
      'right', 'right-top', 'right-bottom'
    ].includes(value)
  },
  offset: {
    type: Number,
    default: 10
  },
  width: {
    type: String,
    default: 'auto'
  },
  // 支持外部控制显示隐藏
  modelValue: {
    type: Boolean,
    default: undefined
  }
});

// 内部状态
const innerVisible = ref(false);
// 如果提供了 modelValue 则使用 modelValue，否则使用内部状态
const visible = computed({
  get: () => props.modelValue !== undefined ? props.modelValue : innerVisible.value,
  set: (val) => {
    if (props.modelValue !== undefined) {
      // 受控模式，触发更新事件
      emit('update:modelValue', val);
    } else {
      // 非受控模式，更新内部状态
      innerVisible.value = val;
    }
    // 触发 visible-change 事件
    emit('visible-change', val);
  }
});

const container = ref<HTMLElement | null>(null);
const triggerEl = ref<HTMLElement | null>(null);
const popover = ref<HTMLElement | null>(null);

const emit = defineEmits(['update:modelValue', 'visible-change']);

const showPopover = () => {
  console.log('showPopover', innerVisible.value);
  visible.value = true;
  nextTick(() => {
    updatePosition();
  });
};

const hidePopover = () => {
  console.log('hidePopover', innerVisible.value);
  visible.value = false;
};

const togglePopover = () => {
  console.log('togglePopover', innerVisible.value);
  visible.value = !visible.value;
  if (visible.value) {
    nextTick(() => {
      updatePosition();
    });
  }
};

const updatePosition = () => {
  if (!triggerEl.value || !popover.value) return;
  
  const triggerRect = triggerEl.value.getBoundingClientRect();
  const popoverRect = popover.value.getBoundingClientRect();
  console.log('popoverRect', popoverRect)
  
  if (props.width !== 'auto') {
    popover.value.style.width = props.width;
  }

  // 重置位置样式
  popover.value.style.top = '';
  popover.value.style.bottom = '';
  popover.value.style.left = '';
  popover.value.style.right = '';

  const placement = props.placement as Placement;
  
  // 根据不同位置设置样式
  if (placement.startsWith('top')) {
    popover.value.style.bottom = `${triggerRect.height + props.offset}px`;
    
    if (placement === 'top') {
      popover.value.style.left = `${(triggerRect.width - popoverRect.width) / 2}px`;
    } else if (placement === 'top-left') {
      popover.value.style.left = '0px';
    } else if (placement === 'top-right') {
      popover.value.style.right = '0px';
    }
  } else if (placement.startsWith('bottom')) {
    popover.value.style.top = `${triggerRect.height + props.offset}px`;
    
    if (placement === 'bottom') {
      popover.value.style.left = `${(triggerRect.width - popoverRect.width) / 2}px`;
    } else if (placement === 'bottom-left') {
      popover.value.style.left = '0px';
    } else if (placement === 'bottom-right') {
      popover.value.style.right = '0px';
    }
  } else if (placement.startsWith('left')) {
    popover.value.style.right = `${triggerRect.width + props.offset}px`;
    
    if (placement === 'left') {
      popover.value.style.top = `${(triggerRect.height - popoverRect.height) / 2}px`;
    } else if (placement === 'left-top') {
      popover.value.style.top = '0px';
    } else if (placement === 'left-bottom') {
      popover.value.style.bottom = '0px';
    }
  } else if (placement.startsWith('right')) {
    popover.value.style.left = `${triggerRect.width + props.offset}px`;
    
    if (placement === 'right') {
      popover.value.style.top = `${(triggerRect.height - popoverRect.height) / 2}px`;
    } else if (placement === 'right-top') {
      popover.value.style.top = '0px';
    } else if (placement === 'right-bottom') {
      popover.value.style.bottom = '0px';
    }
  }
};

const handleClickOutside = (e: MouseEvent) => {
  if (container.value && !container.value.contains(e.target as Node) && visible.value) {
    hidePopover();
  }
};

// 暴露方法给父组件
defineExpose({
  show: showPopover,
  hide: hidePopover,
  toggle: togglePopover
});

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('resize', updatePosition);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('resize', updatePosition);
});

// 监听placement变化，更新位置
watch(() => props.placement, updatePosition);

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal !== undefined && newVal !== innerVisible.value) {
    innerVisible.value = newVal;
    if (newVal) {
      nextTick(updatePosition);
    }
  }
});
</script>

<style scoped>
.popover-container {
  position: relative;
  display: inline-block;
}

.popover {
  position: absolute;
  z-index: 1000;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  max-width: 300px;
}

.popover__content {
  padding: 0;
  /* padding: 10px; */
}

.popover__arrow {
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #fff;
  transform: rotate(45deg);
  z-index: 999;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Top placements */
.popover--top, .popover--top-left, .popover--top-right {
  transform: translateY(-100%);
  margin-bottom: 10px;
}

.popover__arrow--top {
  bottom: -5px;
  left: 50%;
  margin-left: -5px;
  border-top: none;
  border-left: none;
  box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
}

.popover__arrow--top-left {
  bottom: -5px;
  left: 12px;
  border-top: none;
  border-left: none;
  box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
}

.popover__arrow--top-right {
  bottom: -5px;
  right: 12px;
  border-top: none;
  border-left: none;
  box-shadow: 3px 3px 7px rgba(0, 0, 0, 0.07);
}

/* Bottom placements */
.popover--bottom, .popover--bottom-left, .popover--bottom-right {
  margin-top: 0px;
  /* margin-top: 10px; */

}

.popover__arrow--bottom {
  top: -5px;
  left: 50%;
  margin-left: -5px;
  border-bottom: none;
  border-right: none;
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
}

.popover__arrow--bottom-left {
  top: -5px;
  left: 12px;
  border-bottom: none;
  border-right: none;
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
}

.popover__arrow--bottom-right {
  top: -5px;
  right: 12px;
  border-bottom: none;
  border-right: none;
  box-shadow: -2px -2px 5px rgba(0, 0, 0, 0.06);
}

/* Left placements */
.popover--left, .popover--left-top, .popover--left-bottom {
  transform: translateX(-100%);
  margin-right: 10px;
}

.popover__arrow--left {
  right: -5px;
  top: 50%;
  margin-top: -5px;
  border-left: none;
  border-bottom: none;
  box-shadow: 2px -2px 5px rgba(0, 0, 0, 0.06);
}

.popover__arrow--left-top {
  right: -5px;
  top: 12px;
  border-left: none;
  border-bottom: none;
  box-shadow: 2px -2px 5px rgba(0, 0, 0, 0.06);
}

.popover__arrow--left-bottom {
  right: -5px;
  bottom: 12px;
  border-left: none;
  border-bottom: none;
  box-shadow: 2px -2px 5px rgba(0, 0, 0, 0.06);
}

/* Right placements */
.popover--right, .popover--right-top, .popover--right-bottom {
  margin-left: 10px;
}

.popover__arrow--right {
  left: -5px;
  top: 50%;
  margin-top: -5px;
  border-right: none;
  border-top: none;
  box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.06);
}

.popover__arrow--right-top {
  left: -5px;
  top: 12px;
  border-right: none;
  border-top: none;
  box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.06);
}

.popover__arrow--right-bottom {
  left: -5px;
  bottom: 12px;
  border-right: none;
  border-top: none;
  box-shadow: -2px 2px 5px rgba(0, 0, 0, 0.06);
}
</style>

