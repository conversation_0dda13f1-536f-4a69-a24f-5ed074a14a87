<template>
 <div class="live_chat_loading">
          <span class="dot dot_blue"></span>
          <span class="dot dot_red"></span>
      </div>
</template>

<script setup lang="ts">

</script>

<style scoped lang="scss">
.live_chat_loading {
    display: inline-block;
    width: 62px;
    position: relative;
    height: 15px;
    .dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        position: absolute;
    }
    .dot_blue {
        background: #232323;
        left: 0;
        animation: LiveChatLeftToRight 1.5s ease-in-out infinite;
    }
    .dot_red {
        background: #c00000;
        right: 0;
        animation: LiveChatRightToLeft 1.5s ease-in-out infinite;
    }
}
</style>