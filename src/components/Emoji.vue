<template>
  <section>
    <div class="picker_wrap">
      <div class="category">
        <!-- <span>{{ category }}</span> -->
        <div class="emojis_container">
          <span
            @click="handleEmojiClick($event, emoji)"
            v-for="emoji in category_emojis"
            :key="emoji"
          >
            {{ emoji }}
          </span>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import emojiData from "@/assets/emoji-list.js";
import { computed } from "vue";

const emit = defineEmits(["emojiClick"]);
const handleEmojiClick = (e: MouseEvent, emoji: string) => {
  e.preventDefault();
  emit("emojiClick", emoji);
};
const category_emojis = computed(() => {
  return Object.values(emojiData);
});
</script>

<style scoped lang="scss">
.picker_wrap {
  position: relative;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #e5e5e5;
  // height: 200px;
  // overflow: auto;
  margin: 0 -20px 0px -20px;
  padding: 16px 8px 0 20px;
  border-bottom: 1px solid #DEE0E3;
}

.category {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  color: rgb(169, 169, 169);
  height: 200px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 4px;
    background: #f7f7f7;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #9FA2B4;
  }

  &::-webkit-scrollbar-track {
    border-radius: 10px;
    background: #ffffff;
  }
  span {
    @include font14;
  }
}

.emojis_container {
  display: flex;
  flex-wrap: wrap;

  span {
    width: 24px;
    height: 24px;
    margin: 8px;
    margin-left: 0;
    background: inherit;
    font-size: 24px;
    padding: 0;
    cursor: pointer;
  }
}
</style>
