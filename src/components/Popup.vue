// 从底部弹出的弹出层组件
// 使用transition组件实现动画效果
<template>
  <div
    class="modal-backdrop"
    :class="{ show: showModal }"
    @click="closeModal"
  >
    <Transition name="slide-up">
      <div
        class="modal-content"
        @click.stop
        v-show="showModal"
      >
        <slot></slot>
      </div>
    </Transition>
  </div>


</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = withDefaults(defineProps<{
  show: boolean
}>(), {
  show: false
})

const showModal = ref(props.show)
const emit = defineEmits(['close'])

watch(props, (newVal) => {
  console.log('newVal', newVal);
  showModal.value = newVal.show
})

function closeModal() {
  showModal.value = false
  emit('close')
}

</script>


<style scoped lang="scss">
.modal-backdrop {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);

  justify-content: center;
  align-items: flex-end;
  &.show {
    display: flex;
  }
}

.modal-content {
  background: white;
  width: 100%;
  max-width: 500px;
  border-radius: 4px 4px 0px 0px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

/* Transition styles */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}
</style>