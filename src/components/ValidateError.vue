<template>
  <div
    class="validate_error"
    v-if="error"
  >
    <slot></slot>
    <p
      class="error_info"
      v-html="error"
    ></p>
  </div>
</template>

<script setup lang="ts">
type ValidateErrorProps = {
    error: string;
}

withDefaults(defineProps<ValidateErrorProps>(), {
    error: ''
})

</script>

<style scoped lang="scss">
.validate_error {
    display: flex;
    align-items: flex-start;
    padding-top: 4px;
    .error_info {
        flex: 1 1 auto;
        color: $textColor4;
        font-weight: normal;
        @include font13;
    }
}
</style>