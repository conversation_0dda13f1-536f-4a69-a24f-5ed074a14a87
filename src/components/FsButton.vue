<template>
  <div :class="`${type}`" v-if="['video-round', 'video-small', 'video-big'].includes(type)" @click.stop="click">
        <span class="iconfont iconfont_play">&#xf205;</span>
        <slot></slot>
  </div>
  <!-- 普通按钮 -->
  <button
    class="fs-button"
    :disabled="disabled"
    :class="classes"
    :type="'button'"
    @click.stop="click"
    v-else
  >
    <div
      class="box"
      :style="{ opacity: loading ? 0 : 1 }"
    >
      <slot></slot>
      <!-- <span>{{ text }}</span> -->
    </div>
    <div
      class="circle-box"
      v-if="loading"
    >
      <svg
        class="button-circle"
        viewBox="25 25 50 50"
      >
        <circle
          class="button-cir"
          cx="50"
          cy="50"
          r="20"
          fill="none"
          stroke-width="3"
          stroke-miterlimit="10"
        ></circle>
      </svg>
    </div>
  </button>
  <!-- 普通按钮结束 -->
</template>

<script setup lang="ts">
import { computed } from 'vue';
const emit = defineEmits(["click"])

type FsButtonProps = {
  type: string;
  loading?: boolean;
  disabled?: boolean;
  bold?: boolean;
  round?: boolean;
}

const props = withDefaults(defineProps<FsButtonProps>(), {
  type: 'red',
  loading: false,
  disabled: false,
  bold: false,
  round: false,
})

const classes = computed(() => {
  let str = `fs-button-${props.type}${props.loading ? ` fs-button-loading` : ``}${props.disabled ? ` fs-button-disabled` : ``}${props.round ? ` fs-button-round` : ``}`
  return str
})
const click = () => {
  if (!props.loading && !props.disabled) {
    emit('click')
  }
}
</script>

<style scoped lang="scss">
@keyframes rotate {
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }

  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}

@keyframes color {

  0%,
  100% {
    stroke: #fff;
  }

  40% {
    stroke: #fff;
  }

  66% {
    stroke: #fff;
  }

  80%,
  90% {
    stroke: #fff;
  }
}

.button-circle {
  display: inline-block;
  width: 16px;
  height: 16px;
  animation: rotate 2s linear infinite;
}

.button-cir {
  animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  stroke-linecap: round;
}
.video-small {
    display: inline-block;
    position: relative;
    height: 24px;
    line-height: 24px;
    padding: 0 8px;
    border-radius: 4px;
    transition: all 0.3s;
    background: rgba(25, 25, 26, 0.6);
    color: #fff;
    font-size: 12px;
    cursor: pointer;
    &:hover {
        background: rgba(25, 25, 26, 0.8);
    }
    .iconfont_play {
        font-size: 12px;
        margin-right: 4px;
        transform: scale(0.84);
    }
}
/*普通按钮*/
.fs-button {
  display: inline-block;
  position: relative;
  height: 42px;
  transition: all 0.3s;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 3px;
  padding: 0 24px;
  font-weight: 400;
  font-size: 14px;
  line-height: 14px;

  .box {
    display: flex;
    align-items: center;
    position: relative;
    justify-content: center;
    white-space: nowrap;
  }

  .circle-box {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // &.fs-button-gray {
  //     border: 1px solid $borderColor3;
  //     color: $textColor1;
  //     &:hover {
  //         background: $btnBgColor3;
  //         color: #fff;
  //     }
  //     &.fs-button-loading {
  //         background: $btnBgColor3;
  //         opacity: 0.5;
  //     }
  //     &.fs-button-disabled {
  //         color: #fff;
  //         background: $btnBgColor3;
  //     }
  // }
  // &.fs-button-grayline {
  //     border: 1px solid #19191a;
  //     color: $textColor1;
  //     background: #fff;
  //     .button-cir {
  //         animation: dash 1.5s ease-in-out infinite, blackColor 6s ease-in-out infinite;
  //     }
  //     &:hover {
  //         color: #646466;
  //         border: 1px solid #646466;
  //     }
  //     &.fs-button-loading {
  //         opacity: 0.5;
  //     }
  // }
  &.fs-button-lightgray {
    background: #f2f2f2;
    color: $textColor1;

    &:hover {
      background: #e9e9e9;
    }

    &.fs-button-loading {
      opacity: 0.5;
    }
  }

  &.fs-button-round {
    border-radius: 42px;
  }

  &.fs-button-disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
}

.fs-button-red {
  background: #c00000;
  color: #fff;

  &::before {
    display: block;
    content: " ";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    background: #19191a;
    border-radius: 3px;
    opacity: 0;
    transition: all 0.3s;
  }

  &:hover {
    &::before {
      opacity: 0.2;
    }
  }

  @media (max-width: 1024px) {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }

  &.fs-button-loading {
    opacity: 0.6;

    &::before {
      display: block;
      content: " ";
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      position: absolute;
      background: #19191a;
      opacity: 0.2;
      transition: all 0.3s;
    }
  }

  &.fs-button-disabled {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }
}

.fs-button-black {
  background: #4b4b4d;
  color: #fff;

  &::before {
    display: block;
    content: " ";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    background: #19191a;
    border-radius: 3px;
    opacity: 0;
    transition: all 0.3s;
  }

  &:hover {
    &::before {
      opacity: 0.6;
    }
  }

  @media (max-width: 1024px) {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }

  &.fs-button-loading {
    opacity: 0.6;

    &::before {
      display: block;
      content: " ";
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      position: absolute;
      background: #19191a;
      opacity: 0.2;
      transition: all 0.3s;
    }
  }

  &.fs-button-disabled {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }
}

.fs-button-gray {
  background: #f2f2f2;
  color: #19191a;

  .button-cir {
    animation: dash 1.5s ease-in-out infinite, blackColor 6s ease-in-out infinite;
  }

  &::before {
    display: block;
    content: " ";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    background: #19191a;
    border-radius: 3px;
    opacity: 0;
    transition: all 0.3s;
  }

  &:hover {
    &::before {
      opacity: 0.04;
    }
  }

  @media (max-width: 1024px) {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }

  &.fs-button-loading {
    opacity: 0.6;

    &::before {
      display: block;
      content: " ";
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      position: absolute;
      background: #19191a;
      opacity: 0.04;
      transition: all 0.3s;
    }
  }

  &.fs-button-disabled {
    opacity: 0.3;

    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }
}

.fs-button-blackline {
  border: 1px solid #19191a;
  color: $textColor1;

  .button-cir {
    animation: dash 1.5s ease-in-out infinite, blackColor 6s ease-in-out infinite;
  }

  &::before {
    content: " ";
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    position: absolute;
    right: 0;
    background: #19191a;
    border-radius: 3px;
    opacity: 0;
    transition: all 0.3s;
  }

  &:hover {
    &::before {
      opacity: 0.04;
    }
  }

  @media (max-width: 1024px) {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }

  &.fs-button-loading {
    opacity: 0.6;

    &::before {
      content: " ";
      display: block;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      bottom: 0;
      position: absolute;
      right: 0;
      background: #19191a;
      opacity: 0.04;
      transition: all 0.3s;
    }
  }

  &.fs-button-disabled {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }
}

.fs-button-whiteline {
  border: 1px solid #fff;
  color: #fff;

  .button-cir {
    animation: dash 1.5s ease-in-out infinite, blackColor 6s ease-in-out infinite;
  }

  &::before {
    content: " ";
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    position: absolute;
    right: 0;
    background: #fff;
    border-radius: 3px;
    opacity: 0;
    transition: all 0.3s;
  }

  &:hover {
    &::before {
      opacity: 0.04;
    }
  }

  @media (max-width: 1024px) {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }

  &.fs-button-loading {
    opacity: 0.6;

    &::before {
      content: " ";
      display: block;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      bottom: 0;
      position: absolute;
      right: 0;
      background: #fff;
      opacity: 0.04;
      transition: all 0.3s;
    }
  }

  &.fs-button-disabled {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }
}

.fs-button-grayline {
  border: 1px solid #ccc;
  color: $textColor1;

  .button-cir {
    animation: dash 1.5s ease-in-out infinite, blackColor 6s ease-in-out infinite;
  }

  &::before {
    content: " ";
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    bottom: 0;
    position: absolute;
    right: 0;
    background: #19191a;
    border-radius: 3px;
    opacity: 0;
    transition: all 0.3s;
  }

  &:hover {
    &::before {
      opacity: 0.04;
    }
  }

  @media (max-width: 1024px) {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }

  &.fs-button-loading {
    opacity: 0.6;

    &::before {
      content: " ";
      display: block;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      bottom: 0;
      position: absolute;
      right: 0;
      background: #19191a;
      opacity: 0.04;
      transition: all 0.3s;
    }
  }

  &.fs-button-disabled {
    &:hover {
      &::before {
        opacity: 0;
      }
    }
  }
}</style>