<template>
  <section> 
    <Transition
      :name="direction === 'up' ? 'slide-up' : 'slide-down'"
      @before-enter="beforeEnter"
      @enter="enter"
      @afterEnter="afterEnter"
      @before-leave="beforeLeave"
      @leave="leave"
      @after-leave="afterLeave"
    >
      <slot></slot>
    </Transition>
  </section>
</template>

<script setup lang="ts">
interface Props {
  direction?: 'up' | 'down'
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'down'
})

function beforeEnter(el: Element) {
  const ele = el as HTMLElement
  ele.style.height = '0'
  const className = props.direction === 'up' ? 'slide-up' : 'slide-down'
  ele.className = ele.className + " " + className
}

function enter(el:Element) {
  const ele = el as HTMLElement
  ele.style.height = ele.scrollHeight + 'px'
}

function afterEnter(el: Element) {
  const ele = el as HTMLElement
  const className = props.direction === 'up' ? 'slide-up' : 'slide-down'
  ele.className = ele.className.replace(className, "")
  ele.style.height = ""
}

function beforeLeave(el: Element) {
  const ele = el as HTMLElement
  ele.style.height = el.scrollHeight + "px"
}

function leave(el: Element) {
  const ele = el as HTMLElement
  ele.style.height = '0'
  const className = props.direction === 'up' ? 'slide-up' : 'slide-down'
  ele.className = ele.className + " " + className
}

function afterLeave(el: Element) {
  const ele = el as HTMLElement
  const className = props.direction === 'up' ? 'slide-up' : 'slide-down'
  ele.className = ele.className.replace(className, "")
  ele.style.height = ""
}
</script>

<style scoped lang="scss">
.slide-down, .slide-up {
    transition: 0.3s height ease-in-out, 0.3s padding-top ease-in-out,
        0.3s padding-bottom ease-in-out;
    overflow: hidden;
}

.slide-up-enter-from,
.slide-up-leave-to {
    transform: translateY(20px);
    opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
    transition: all 0.3s ease;
}

.slide-up-enter-to,
.slide-up-leave-from {
    transform: translateY(0);
    opacity: 1;
}
</style>