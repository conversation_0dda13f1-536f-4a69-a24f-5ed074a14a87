// 编写一个vue指令，用于监听点击事件，当点击的元素不在指令所在的元素内时，触发回调函数。
import { DirectiveBinding } from "vue";
interface ClickOutsideElement extends HTMLElement {
  clickOutsideHandler?: (event: MouseEvent) => void;
}
export default {
  mounted(el: ClickOutsideElement, binding: DirectiveBinding) {
    // 定义点击事件处理函数
    el.clickOutsideHandler = (event: MouseEvent) => {
      // 检查点击是否发生在绑定元素的外部
      if (!el.contains(event.target as Node)) {
        // 如果是，执行传入的回调函数
        binding.value(event);
      }
    };
    // 在捕获阶段添加事件监听
    document.addEventListener("click", el.clickOutsideHandler, true);
  },
  unmounted(el: ClickOutsideElement) {
    // 移除事件监听器
    if (el.clickOutsideHandler) {
      document.removeEventListener("click", el.clickOutsideHandler, true);
    }
  },
};