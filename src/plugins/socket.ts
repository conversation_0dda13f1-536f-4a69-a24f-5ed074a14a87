import { provide, inject } from 'vue'

import Ws from '@/util/FsWs'
import { getAllQueryStrings } from '@/util/util'

export const initSocket = () => {
  // appId language isoCode clientToken fromPage webSite
  const { appId, isoCode, language, webSite, clientUserInfo, fromPage } =
    getAllQueryStrings()
  const params = {
    url: import.meta.env.VITE_SOCKET_URL,
    params: {
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      licenseId: '111293',
      appId,
      isoCode,
      language,
      webSite,
      fromPage: fromPage || window.location.href,
    },
    clientUserInfo,
  }
  const wsInstance = Ws.getInstance(params)
  return wsInstance
}

export function useWsInstance(): typeof Ws {
  const wsInstance = initSocket()
  provide('wsInstance', wsInstance)

  return wsInstance
}

export function injectWsInstance(): typeof Ws {
  const wsInstance = inject<typeof Ws>('wsInstance')
  if (!wsInstance) {
    throw new Error('Ws instance not provided.')
  }
  return wsInstance
}
