import {simpleToTradition} from 'chinese-simple2traditional'
import i18n from '../lang/index'
import { getCurrentInstance } from 'vue'
import { getQueryString } from '../util/util';
const translateToTraditionalChinese = (str: string) => {
  return simpleToTradition(str)
}
/**
 * 为了在ts中使用$c，需要在vue的声明文件中声明$c
 * 可以在ts文件中直接声明，也可以在*.d.ts中声明
 * 在*.d.ts中声明的好处是，需要需要包含至少一个顶级的 import 或 export，即使它只是 export {}。如果扩展被放在模块之外，它将覆盖原始类型，而不是扩展它。
 */
declare module 'vue' {
  interface ComponentCustomProperties {
    $c: (key: string) => string
  }
}
const i18nC = {
  install: (app: any) => {
    app.config.globalProperties.$c = function (key: string) {
      const siteCode = getQueryString('webSite')
      if (['tw', 'hk', 'mo'].includes(siteCode)) {
        i18n.global.locale.value = 'cn'
        const translatedKey = translateToTraditionalChinese(i18n.global.t(key))
        return translatedKey
      } else {
        return i18n.global.t(key)
      }
    }
  },
}
/**
 * 将已经挂载到全局的$c导出，方便在ts中使用
 * 只要在 setup() 内部调用的任何方法都可以获取当前实例，无论函数调用栈有多深。
 * getCurrentInstance() 返回一个组件实例对象，或者如果当前上下文不是组件初始化的过程，则返回 null。
 * 它只能在同步代码中访问。例如 setTimeout DOM 事件 Promise ：无法访问这种类型的异步代码
 * 
 */
export const $c = (key: string) => {
   return getCurrentInstance()?.proxy.$c(key)
 }
export default i18nC