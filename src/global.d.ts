
declare module '@/assets/emoji-list.js' {
  const emojiList: {
    [key: string]: string
  }
  export default emojiList
}

declare interface MessageBody {
  type: number;
  avatar?: string;
  canSave?: boolean;
  extraData?: ServeType | any;
  fromUserId?: string;
  groupId?: string;
  messageId?: string;
  messageReadStatus?: boolean;
  messageTime?: string;
  messageType?: number;
  messageVisibilityType?: number;
  msg?: string;
  name?: string | Ref<string>;
  traceId?: string;
  userId?: number;
  email?: string | Ref<string>;
  leaveMsg?: string | Ref<string>;
  customerName?: string | Ref<string>;
  customerServiceName?: string;
  requestType?: number;
  mobile?: string | Ref<string>;
  isLoading?: Ref<boolean>;
  isShowEmail?: Ref<boolean>;
  isShowName?: Ref<boolean>;
  isShowMobile?: Ref<boolean>;
  suffixStatus?: Ref<number>;
  hideMessageId?: string;
  token?: string;
  clientMessageId: string;
  fileInfo?: FileInfoType;
  applyError?: Ref<ApplyFormError>;
  groupManageId?: number;
  isOnline?: boolean;
  formType?: number;
  hasTyping?: boolean;
  hasCustomerServiceToRoom?: boolean;
  feedbackMsg?: string;
  feedbackScore?: number;
  groupStr?: string;
  uuid?: string;
  comments?: string;
  score?: number;
}

declare interface ServeType {
  content?: string
  id?: number
  level?: number
  type?: number
  grade?: number
}
declare type FileInfoType = {
  fileName: string
  fileUrl: string
  fileExt: string
  fileSize: string
  formatName?: string
  originSize?: number
}

declare type ApplyFormError = {
  name?: string
  email?: string
  mobile?: string
}