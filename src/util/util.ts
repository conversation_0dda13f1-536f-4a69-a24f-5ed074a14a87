import ByteBuffer from './byteBuffer'
import Base64 from './base64'
import xss from 'xss'
// import sensitiveWords from '@/assets/sensi_words.json'

import Mint from 'mint-filter'
import request from './request'
import { MD5 } from 'crypto-js'
import axios from 'axios'
import { sign } from './crypto'
const b64 = new Base64();
const sensitiveList: any[] = []
let whiteWordList: any[] = null
let mint: Mint | null = null

fetchSensitiveWords();

export const formatDate = (time: number, siteCode: string) => {
  if (typeof time === 'string') {
    time = Number(time)
  }
  let d = new Date(time)
  let hh = d.getHours()
  let m = d.getMinutes()
  let s = d.getSeconds()
  let dd
  let h = hh
  if (siteCode === 'de') {
    dd = 'Uhr'
  } else {
    if (h >= 12) {
      h = hh - 12
      switch (siteCode) {
        case 'ru':
          dd = 'вечера'
          break
        case 'jp':
          dd = '午後'
          break
        default:
          dd = 'PM'
          break
      }
    } else {
      switch (siteCode) {
        case 'ru':
          dd = 'утра'
          break
        case 'jp':
          dd = '午前'
          break
        default:
          dd = 'AM'
          break
      }
    }
    if (h == 0) {
      h = 12
    }
  }
  let mm = m < 10 ? '0' + m : m
  let ss = s < 10 ? '0' + s : s
  let replacement
  if (siteCode !== 'jp') {
    replacement = `${h}:${mm} ${dd}`
  } else {
    replacement = `${dd} ${h}:${mm}`
  }
  return replacement
}

// 将后端返回的UTC时间转换为本地时间
export const utcToLocal = (utcTime: string) => {
  const nd = new Date(Number(utcTime))
  if (isNaN(nd.getTime())) return utcTime
  const today = new Date()
  const isToday = nd.toDateString() === today.toDateString()

  if (isToday) {
    // 格式化为12小时制的时分（am/pm）
    let hours = nd.getHours()
    const minutes = nd.getMinutes().toString().padStart(2, '0')
    const amPm = hours >= 12 ? 'PM' : 'AM'
    hours = hours % 12 || 12
    return `${hours}:${minutes} ${amPm}`
  } else {
    // 在函数内部获取站点代码
    const siteCode = getQueryString('webSite') || 'en'
    const year = nd.getFullYear()
    const month = nd.getMonth() + 1
    const day = nd.getDate()

    switch (siteCode) {
      case 'en': // US站 MM/DD/YYYY
        return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`
      case 'uk': // UK站 DD/MM/YYYY
      case 'de': // DE站 DD/MM/YYYY
      case 'sg': // SG站 DD/MM/YYYY
      case 'au': // AU站 DD/MM/YYYY
      case 'nl': // NL站 DD/MM/YYYY
        return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`
      case 'jp': // JP站 YYYY/MM/DD
        return `${year}/${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}`
      case 'cn': // CN站 YYYY-MM-DD
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
      default:
        // 默认使用US格式
        return `${month.toString().padStart(2, '0')}/${day.toString().padStart(2, '0')}/${year}`
    }
  }
}

// 生成UUID
export const generateUUID = () => {
  let d = new Date().getTime()
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = ((d + Math.random() * 16) % 16) | 0
    d = Math.floor(d / 16)
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16)
  })
  return uuid
}
export const packMsg = (msg: any, option: Record<string, any> = {}) => {
  const { version = 1, messageType = 2, sequenceId = 0, seqType = 1 } = option
  const msgBuf = new ByteBuffer()
  const magic = [108, 105, 118, 101, 99, 104, 97, 116]
  const token = localStorage.getItem('live_chat_token') || ''
  msg.headers = {
    Authorization: token,
  }
  const s = b64.strToBase64(JSON.stringify(msg))
  const bodyLen = getLen(s)
  msgBuf
    .byteArray(magic, magic.length)
    .byte(version)
    .byte(seqType)
    .byte(messageType)
    .int64(sequenceId)
    .byte(0x1)
    .int32(bodyLen)
    .vstring(s, bodyLen)
  console.log('------sendmessage:发送消息', msg, option.messageType)
  return msgBuf.pack()
}

export const unpackMsg = (e: MessageEvent) => {
  // 消息解包
  const byteBuffer = new ByteBuffer(e.data as Buffer)
  const read = byteBuffer
    .byteArray(null, 8, null)
    .byte()
    .byte()
    .byte()
    .int64()
    .byte()
    .int32()
    .unpack()
  const bodyLen = read[6]
  const data = byteBuffer.vstring(null, bodyLen).unpack()
  const decodeBase64 = b64.base64ToStr(data[7])
  const parse = JSON.parse(decodeBase64)
  return parse
}
export const checkDataType = (data: any)  => {
  //转换类型
  return new Promise((resolve, reject) => {
    console.log(data)
    if (data instanceof ArrayBuffer) {
      resolve(data)
    } else if (data instanceof Blob) {
      const reader = new FileReader()
      reader.onloadend = function () {
        resolve(reader.result)
      }
      reader.readAsArrayBuffer(data)
    } else {
      reject()
    }
  })
}
const getLen = (str: string) => {
  let len = 0
  for (let i = 0; i < str.length; i++) {
    const c = str.charCodeAt(i)
    //单字节加1
    if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
      len++
    } else {
      len += 3
    }
  }
  return len
}

// 节流函数
export const throttle = (fn: Function, delay: number) => {
  let flag = true
  return function (this:any) {
    if (!flag) return
    flag = false
    setTimeout(() => {
      fn.apply(this, arguments)
      flag = true
    }, delay)
  }
}
// 防抖函数
export const debounce = (fn: Function, delay: number) => {
  let timer: any = null
  return function (this:any) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, arguments)
    }, delay)
  }
}

// 将文字中的url转换为a标签
export const urlToLink = (str: string) => {
  if (!str || str.includes('<a')) return str
  const reg = /(http:\/\/|https:\/\/)((\w|=|\?|\.|\/|&|-)+)/g
  return str.replace(reg, '<a href="$1$2" target="_blank">$1$2</a>')
}

// 防止xss攻击
export const xssFilter = (str: string) => { 
  const options = {
    whiteList: {
      // a: ['href', 'title', 'target'],
    },
  }
  return xss(str, options)
}

// 获取某个url参数
export const getQueryString = (name: string) => { 
  const urlParams = new URLSearchParams(window.location.search)
  return urlParams.get(name)
}
//获取所有的url参数,返回一个对象
export const getAllQueryStrings = () => { 
  const urlParams = new URLSearchParams(window.location.search)
  const params:Record<string, any> = {}
  for (const [key, value] of urlParams) {
    params[key] = value
  }
  return params
}
// 校验邮箱正确性
export const validateEmail = (email: string) => {
  const reg = /[A-Za-z0-9]+([-_.][A-Za-z0-9]+)*@([A-Za-z0-9]+[-.])+[A-Za-z]{2,4}$/
  return reg.test(email) && !email.includes(" ");
}

// 校验手机号正确性 
export const validatePhone = (phone: string) => {
  const reg = /^[1][3-8]\d{9}$|^([6|9])\d{7}$|^[0][9]\d{8}$|^6\d{5}$/ //中国大陆+港澳台手机验证
  return reg.test(phone)
}
// 通过url下载文件
export const downloadFileByUrl = async (url: string, fileName: string) => { 
  const baseUrl = import.meta.env.VITE_S3_URL
  const apiUrl = `${baseUrl}/api/s3/getFileByUrl?filename=${fileName}&url=${url}`
  
  // 生成签名token
  const appId = "6016e89e3dc247d185f590cb0f5aa005";  // 应用ID
  const secret = "F41McYiryC31gQDro6ji";  // 密钥
  const token = sign(appId, secret)

  try {
    const response = await axios({
      method: 'get',
      url: apiUrl,
      responseType: 'blob',
      headers: {
        'Authorization': token
      }
    })

    // 创建Blob链接并触发下载
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = downloadUrl
    a.download = fileName
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(downloadUrl)
    document.body.removeChild(a)
  } catch (error) {
    console.error('Download file failed:', error)
  }
}
export const getAssetURL = (image:string) => {
  // 参数一: 相对路径
  // 参数二: 当前路径的URL
  return new URL(`../assets/svg/${image}`, import.meta.url).href
}

export const parseJWT = () => {
  const token = localStorage.getItem('live_chat_token')
  if (!token) return
  const base64Url = token.split('.')[1]
  const base64 = base64Url.replace('-', '+').replace('_', '/')
  return JSON.parse(window.atob(base64))
}

// 判断当前系统是移动端还是pc端(排除ipad)
export const isMobile = () => {
  const userAgent = navigator.userAgent || navigator.vendor || (window as any).opera;
  // 常见移动端UA关键字
  return /android|webos|iphone|ipod|blackberry|iemobile|opera mini|windows phone/i.test(userAgent);
}

export const getDeviceType = () => {
  const width = window.innerWidth;
  if (width <= 767) {
    return "mobile"; // 手机端
  } else if (width > 767 && width <= 1024) {
    return "pad"; // 平板端
  } else {
    return "pc"; // PC端
  }
};
/**
 * @description 判断操作系统是android还是ios
 * 
 */
export const isAndroid = () => {
  const u = navigator.userAgent
  const isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1 //android终端
  // const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/) //ios终端
  return isAndroid
}

export const dataLayerToParent = (data: any) => {
  window.parent.postMessage({
    type: 'dataLayer',
    data
  }, '*')
} 
// export function makeSensitiveMap() {
//   const result = {}
//   const count = sensitiveWords.length
//   // 依次取字
//   for (let i = 0; i < count; ++i) {
//     let map:any = result
//     const word = sensitiveWords[i]
//     // 依次获取字
//     for (let j = 0; j < word.length; ++j) {
//       const ch = word.charAt(j) // charAt() 方法可返回指定位置的字符。
//       // 判断是否存在
//       if (typeof map[ch] != 'undefined') {
//         map = map[ch]
//         if (map['empty']) {
//           break
//         }
//       } else {
//         if (map['empty']) {
//           delete map['empty']
//         }
//         map[ch] = { empty: true }
//         map = map[ch]
//       }
//     }
//   }
//   return result
// }

// export function checkSensitiveWord(sentence: string) {
//   let result = []
//   let count = sentence.length
//   let stack = []
//   let point = sensitiveMap as any
//   for (let i = 0; i < count; i++) {
//     const ch = sentence.charAt(i)
//     const item = point[ch]
//     if (typeof item == 'undefined') {
//       i = i - stack.length
//       stack = []
//       point = sensitiveMap
//     } else if (item['empty']) {
//       stack.push(ch)
//       result.push(stack.join(''))
//       stack = []
//       point = sensitiveMap
//     } else {
//       stack.push(ch)
//       point = item
//     }
//   }
//   return result
// }
// 根据敏感词库过滤敏感词
export const filterSensitiveWords = (str: string) => {
  if (!mint) {
    return { result: str, hasSensitiveWords: false }
  }
  let whiteWordSave: string[] = []
  let i = 0
  whiteWordList.forEach((word) => {
    if (str.includes(word)) {
      whiteWordSave.push(word)
      str = str.replace(new RegExp(word, 'g'), '$_' + i)
      i = i + 1
    }
  })
  const { text, words } = mint.filter(str)
  let result = text
  //根据whiteWordSave的index和$_index替换回来
  whiteWordSave.forEach((item, index) => {
    result = result.replace(new RegExp('\\$_' + index, 'g'), item)
  })
  if (words.length > 0) {
    return { result, hasSensitiveWords: true }
  } else {
    return { result, hasSensitiveWords: false }
  }
}
async function fetchSensitiveWords(): Promise<any> {
  // 获取本地时间同时满足YYYY-MM-DD HH:mm:ss格式
  // const date = new Date()
  // const year = date.getFullYear()
  // const month = (date.getMonth() + 1).toString().padStart(2, '0')
  // const day = date.getDate().toString().padStart(2, '0')
  // const hour = date.getHours().toString().padStart(2, '0')
  // const minute = date.getMinutes().toString().padStart(2, '0')
  // const second = date.getSeconds().toString().padStart(2, '0')
  // const timestamp = `${year}-${month}-${day} ${hour}:${minute}:${second}`
  // const key =
  //   import.meta.env.MODE === 'production'
  //     ? '77c7b9068f1e4e74938391186a44b83b'
  //     : 'e0716a18111f45c1a2284aeed072aa7f'
  // const sign = MD5('livechat' + key + timestamp).toString()
  // return request('/cms/api/outApi/getSensitiveWords', {
  //   method: 'post',
  //   headers: {
  //     'client-id': 'livechat',
  //     timestamp,
  //     sign,
  //   },
  // })
  try {
    const url =
      import.meta.env.MODE === "production"
  ? "https://resource.fs.com/mall/sensitive-word/sensitive-words.json"
        : "https://resource.fs.com/mall/sensitive-word/sensitive-words-test.json";
    const { data } = await fetch(`${url}?timestamp=${new Date().getTime()}`).then((res) => res.json());
    if (data?.sensitiveWords?.length > 0) {
      const list = data.sensitiveWords;
      for (const item of list) {
        if (item.sensiType === 1) {
          sensitiveList.push(b64.base64ToStr(item.sensiWord));
        }
      }
      whiteWordList = list
        .filter((item: any) => item.sensiType === 3)
        .map((item: any) => b64.base64ToStr(item.sensiWord));
      mint = new Mint(sensitiveList);
    }
  } catch (error) {
    console.log(error);
  }
}
// 创建单例
export function singleton<T extends (target: any, args: any[]) => T>(className: any) {
  let instance: T;
  const proxy = new Proxy(className, {
    construct(target, args) {
      if (!instance) {
        instance = new className(...args);
      }
      return instance;
    }
  })
  className.prototype.constructor = proxy;
  return proxy;
}