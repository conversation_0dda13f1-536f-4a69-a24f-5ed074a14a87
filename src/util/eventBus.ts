export default class EventBus {
  private static instance: EventBus;
  private eventMap: Map<string, Function[]>;

  constructor() {
    this.eventMap = new Map();
  }

  static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    return EventBus.instance;
  }

  on(eventName: string, callback: Function) {
    const callbacks = this.eventMap.get(eventName);
    if (callbacks) {
      callbacks.push(callback);
    } else {
      this.eventMap.set(eventName, [callback]);
    }
  }

  emit(eventName: string, ...args: any[]) {
    const callbacks = this.eventMap.get(eventName);
    if (callbacks) {
      callbacks.forEach((callback) => {
        callback(...args);
      });
    }
  }

  off(eventName: string, callback: Function) {
    const callbacks = this.eventMap.get(eventName);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }
}