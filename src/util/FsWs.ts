import crypto, { base64Encrypt } from './crypto'
import { sendLocation } from './geolocation'
import { packMsg, singleton, unpackMsg } from './util'

class Ws {
  static instance: Ws = null
  //单例
  static getInstance(params = {}) {
    if (!Ws.instance) {
      Ws.instance = new Ws(params)
    }
    return Ws.instance
  }
  private ws: WebSocket = null
  private url: string
  private params: Record<string, any>
  private heartTimer: any = null
  private heartInterval = 50000 // 心跳间隔
  private heartMsg = {}
  private lockReconnect = false
  private reconnectCount = 0
  private reconnectMaxCount = 10
  private isReconnected = false // 是否重连过
  private reconnectTimer: any = null
  private reconnectInterval = 5000 // 重连间隔
  private isReconnectionLoading = false // 是否在重连中
  private clientUserInfo = ''
  private chatToken =
    (window.localStorage && localStorage.getItem('live_chat_token')) || ''
  //重连回调
  private reconnectCallback: () => void | null
  //消息回调
  private messageCallback: (e: any) => void | null
  //关闭回调
  private closeCallback: (e: any) => void | null
  //错误回调
  private errorCallback: (e: any) => void | null
  //连接成功回调
  private openCallback: () => void | null
  //网络连接正常回调
  private networkOnlineCallback: (e: any) => void | null
  //网络连接失败回调
  private networkOfflineCallback: (e: any) => void | null
  private networkStatus: 'online' | 'offline' = 'online'
  //连接状态
  private status = 0
  //连接状态
  private statusMap: any = {
    0: '未连接',
    1: '连接中',
    2: '已连接',
    3: '连接关闭',
    4: '连接错误',
    5: '连接中断',
    6: '连接超时',
  }
  private eventMap: Array<Record<string, string>> = [
    {
      type: 'open',
      callback: 'openCallback',
    },
    {
      type: 'message',
      callback: 'messageCallback',
    },
    {
      type: 'close',
      callback: 'closeCallback',
    },
    {
      type: 'error',
      callback: 'errorCallback',
    },
    {
      type: 'reconnect',
      callback: 'reconnectCallback',
    },
    {
      type: 'online',
      callback: 'networkOnlineCallback',
    },
    {
      type: 'offline',
      callback: 'networkOfflineCallback',
    },
  ]
  constructor(option: Record<string, any>) {
    this.url = option.url
    this.params = option.params
    // 从客户端页面登录获取的token
    this.clientUserInfo = option?.clientUserInfo || ''
    this.init()
  }
  private init() {
    this.createWebSocket()
    this.checkNetworkStatus()
  }
  private checkNetworkStatus = () => {
    window.addEventListener('online', (e) => {
      this.networkStatus = 'online'
      this.networkOnlineCallback(e)
    })
    window.addEventListener('offline', (e) => {
      this.networkStatus = 'offline'
      this.networkOfflineCallback(e)
    })
  }
  public createWebSocket() {
    this.status = 1
    const searchParams = new URLSearchParams(this.params)
    this.chatToken = localStorage.getItem('live_chat_token') || ''
    const protocols = {
      clientUserInfo: this.clientUserInfo,
      chatToken: this.chatToken,
      ...crypto,
    }
    const encryptText = base64Encrypt(JSON.stringify(protocols))
    const queryString = searchParams.toString()
    this.ws = new WebSocket(
      `${this.url}?${queryString}`,
      encodeURIComponent(encryptText) // 传递加密参数
    )
    this.ws.binaryType = 'arraybuffer' //设置返回类型为Arraybuffer
    this.ws.onopen = this.onOpen.bind(this)
    this.ws.onmessage = this.onMessage.bind(this)
    this.ws.onclose = this.onClose.bind(this)
    this.ws.onerror = this.onError.bind(this)
  }

  // 添加重置方法，用于组件重新打开时重置状态
  public resetConnectionState() {
    console.log('-----重置连接状态-----')
    this.isReconnected = false
    this.reconnectCount = 0
    this.lockReconnect = false
    this.isReconnectionLoading = false
  }

  private onOpen(e: Event) {
    console.log('---onopen:连接打开', e)
    this.status = 2
    this.lockReconnect = false //重置重连锁
    this.isReconnectionLoading = false
    this.heartCheck()
    this.sendMsg({}, { messageType: 84 });
  }

  private onMessage(e: MessageEvent) {
    try {
      const parse = unpackMsg(e)
      console.log('-----onmessage:接收消息', parse)
      this.messageCallback && this.messageCallback(parse)
      this.handleMessageType(parse)
    } catch (error) {
      console.log(error)
    }
  }
  private onClose(e: CloseEvent) {
    console.log('----close:关闭连接', e.code, e.reason, e.wasClean)
    this.status = 3
    this.reconnect()
    this.isReconnectionLoading = false
  }
  private onError(e: Event) {
    console.log('------error:连接错误', e)
    this.status = 4
    this.reconnect()
    this.isReconnectionLoading = false
  }
  //获取连接状态
  public getStatus() {
    return { status: this.status, msg: this.statusMap[this.status] }
  }
  //添加事件监听
  public on(type: string, callback: any) {
    this.eventMap.map((item: Record<string, string>) => {
      if (item.type === type) {
        this[item.callback as keyof Ws] = callback
      }
    })
  }
  //移除事件监听
  public off(type: string) {
    this.eventMap.map((item: Record<string, string>) => {
      if (item.type === type) {
        this[item.callback as keyof Ws] = null
      }
    })
  }
  public sendMsg(msg: any, option: Record<string, any> = {}) {
    if (this.networkStatus === 'offline') return
    const packMessage = packMsg(msg, option)
    this.ws.send(packMessage)
  }
  public close() {
    console.log('-----主动关闭-----')
    this.status = 5
    this.lockReconnect = true
    this.isReconnected = false // 重置重连状态
    this.reconnectCount = 0 // 重置重连次数
    clearTimeout(this.heartTimer)
    clearTimeout(this.reconnectTimer)
    //服务器会主动关闭连接，同时手动关闭
    this.ws.close()
  }
  private heartCheck() {
    clearTimeout(this.heartTimer)
    this.heartTimer = null
    if (this.ws.readyState !== 1) return
    this.heartTimer = setTimeout(() => {
      this.sendMsg(this.heartMsg, { messageType: 14 })
      this.heartCheck()
    }, this.heartInterval)
  }
  public reconnect(options: {isForce: boolean} = {isForce: false}) {
    console.log('----进去重连---：重连锁状态', this.lockReconnect)
    // 防止重复重连
    if (this.lockReconnect || this.isReconnectionLoading) return
    console.log(
      '-----reconnect---: 开始重连，重连次数',
      this.reconnectCount,
      this.reconnectMaxCount
    )
    this.isReconnectionLoading = true
    clearTimeout(this.reconnectTimer)
    this.reconnectTimer = null
    if (this.reconnectCount < this.reconnectMaxCount) {
      this.reconnectCount++
      this.reconnectTimer = setTimeout(() => {
        this.isReconnected = true
        this.createWebSocket()
      }, this.reconnectInterval)
    } else {
      if (options.isForce) {
        this.reconnectCount = 0;
        this.reconnectTimer = setTimeout(() => {
          this.isReconnected = true;
          this.createWebSocket();
        }, this.reconnectInterval);
      }
    }
    this.status = 6
  }
  private firstConnection() {
    //第一次连接成功直接发送22类型消息
    // 修复：在发送消息前先保存当前的重连状态，然后再重置
    const isReconnect = this.isReconnected
    console.log('-----firstConnection: isReconnected=', this.isReconnected, 'reconnectCount=', this.reconnectCount)
    this.sendMsg(
      { isReconnect: isReconnect },
      { messageType: 22 }
    )
    this.reconnectCount = 0 //重连次数重置
    this.isReconnected = false //重置重连状态
  }

  private handleMessageType(mes: any) {
    if (mes.messageType === 35) {
      this.firstConnection()
    } else if (mes.messageType === 15) {
      window.localStorage &&
        window.localStorage.setItem('live_chat_token', mes.token)
      //获取地理位置
      sendLocation((locationInfo) => {
        this.sendMsg(locationInfo, {messageType: 79})
      })
    } else if (mes.messageType === 16) {
      console.error(mes.msg)
    }
  }
}
export default singleton(Ws)
