import { HmacSHA256, AES, enc, mode, pad, MD5 } from "crypto-js";
import Base64 from "./base64";

const b64 = new Base64();
const privateKey = "4c9fd5122688c7a4";
const publicKey = "87b579c7ab2ea5f8";
//生成随机字符串的方法
export function randomString() {
  const randomString = [...window.crypto.getRandomValues(new Uint8Array(8))]
    .map((x) => x.toString(16))
    .join("");
  return randomString;
}

export const generateHmacString = (params = "") => {
  const nonce = randomString();
  const timestamp = new Date().getTime();
  const authToken = HmacSHA256(nonce + timestamp, privateKey).toString();
  return {
    authToken,
    nonce,
    timestamp,
    clientSignature: authToken,
  };
};
export const AESCrypto = {
  encrypt(str: string, KEY: string, IV: string) {
    var key = enc.Utf8.parse(KEY);
    var iv = enc.Utf8.parse(IV);
    var encrypted = AES.encrypt(str, key, {
      iv: iv,
      mode: mode.CBC,
      padding: pad.Pkcs7,
    });
    return encrypted.toString();
  },
  //解密
  decrypt(str: string, KEY: string, IV: string) {
    var key = enc.Utf8.parse(KEY);
    var iv = enc.Utf8.parse(IV);
    var decrypt = AES.decrypt(str, key, {
      iv: iv,
      mode: mode.CBC,
      padding: pad.Pkcs7,
    });
    return decrypt.toString(enc.Utf8);
  },
};
export const base64Encrypt = (str: string) => {
  const base64 = b64.strToBase64(str);
  return base64;
};
export default {
  publicKey,
  ...generateHmacString(),
};

/**
 * 生成签名
 * @param {string} appId - 应用 ID
 * @param {string} secret - 应用密钥
 * @returns {string} - 签名后的 Base64 字符串
 */
export function sign(appId: string, secret: string) {
  if (!appId || !secret) {
    throw new Error("sign appId/secret error");
  }

  // 生成10位随机字符串
  const ranStr = randomString().slice(0, 10);

  // 获取当前秒级时间戳
  const stamp = Math.floor(Date.now() / 1000);

  // 构造 query 字符串
  const query = `k=${appId}&r=${ranStr}&t=${stamp}&f=fs`;

  // 计算 HMAC-SHA256
  const hex = HmacSHA256(query, secret).toString();

  // Base64 编码
  return b64.strToBase64(hex + query);
}

