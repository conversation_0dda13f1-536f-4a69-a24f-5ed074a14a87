import crypto, { generateHmacString }  from './crypto'
// 引入axios依赖包
import axios from 'axios'
// 创建axios实例,设置baseUrl和超时时间
const service = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 30000
})

// 请求拦截器
service.interceptors.request.use(
    config => {
        // 在发送请求之前做些什么
        const {url} = config 
        if (url === "/livechat/message/customer/list") {
          const data = config.data
          const { clientSignature, nonce, timestamp } = generateHmacString(JSON.stringify(data));
          config.headers.nonce = nonce;
          config.headers.timestamps = timestamp;
          config.headers.clientSignature = clientSignature;
        } else {
          config.headers.clientSignature = crypto.clientSignature;
          config.headers.nonce = crypto.nonce;
          config.headers.timestamps = crypto.timestamp;
        }
        config.headers.Accept = 'application/json;charset=UTF-8'
        config.headers.apiKey = crypto.publicKey
        return config
    }
)
// 响应拦截器
service.interceptors.response.use(
    response => {
        const headers = response.headers
        const type = headers['content-type']
        if (['application/octet-stream'].includes(type)) {
          return Promise.resolve(response.data)
        }
        // 对响应数据做点什么
        if (response.data.status === '200' || response.data.code === '200' || response.data.code === 200) {
          return Promise.resolve(response.data.data)
        } else {
          return Promise.reject(response?.data?.message)
        }
    }
)
// 封装get请求
export const get = (url: string, params = {}, options = {}) => { 
    return new Promise((resolve, reject) => {
        service.get(url, {
            params,
            ...options
        }).then(response => {
            resolve(response)
        }).catch(error => {
            reject(error)
        })
    })
}
// 封装post请求
export const post = (url: string, data = {}, option = {}) => {
    return new Promise((resolve, reject) => {
        service.post(url, data, option).then(response => {
            resolve(response)
        }).catch(error => {
            reject(error)
        })
    })
}

export default service