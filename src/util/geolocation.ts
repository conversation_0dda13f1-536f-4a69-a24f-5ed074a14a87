import request from "./request"
import { parseJWT } from "./util"

//`纬度：${position.coords.latitude}, 经度：${position.coords.longitude}`
type PositionType = {
  latitude: number
  longitude: number
}
type sendCallback = (info: PositionType) => void
const getLocation = () => {
  return new Promise((resolve, reject) => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const localeInfo = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          }
          resolve(localeInfo)
        },
        (error) => {
          switch (error.code) {
            case error.PERMISSION_DENIED:
              console.log('用户拒绝了地理位置请求。')
              break
            case error.POSITION_UNAVAILABLE:
              console.log('无法获取用户的地理位置信息。')
              break
            case error.TIMEOUT:
              console.log('获取地理位置超时。')
              break
          }
          reject(error.message)
        }
      )
    } else {
      console.log('抱歉，您的浏览器不支持地理位置功能。')
      reject(new Error('浏览器不支持地理位置功能。'))
    }
  })
}

export const sendLocation = async (cb: sendCallback) => {
  try {
    const localeInfo = await getLocation()
    // await sendUserLocation(localeInfo as PositionType)
    cb && cb(localeInfo as PositionType)
  } catch (error) {
    console.log(error)
  }
}
export const sendUserLocation = async (info: PositionType) => {
  const userInfo = parseJWT()
  return request.put('/livechat/users/update-device', {
    deviceId: userInfo.deviceId,
    userId: userInfo.userId,
    latitude: info.latitude,
    longitude: info.longitude,
  })
}