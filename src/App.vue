/**
* setup是位于beforeCreate和created之间的一个新的生命周期钩子，在setup中我们可以使用props、data、methods、computed、watch等，但是不能使用this
*/
<script setup lang="ts">
import LiveChat from '@/views/LiveChat.vue';
import PrivacyPolicy from '@/views/components/PrivacyPolicy/index.vue';
import { onMounted, ref, inject, onBeforeUnmount, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { dataLayerToParent, getQueryString, getAllQueryStrings, isMobile } from './util/util';
import EventBus from './util/eventBus';

const $bus = inject<EventBus>('eventBus')
const appId = getQueryString('appId')
const liveChatStatus = ref(appId !== '2' ? false : true) // liveChat的html是否显示
const componentStatus = ref(appId !== '2' ? false : true) // 组件的状态
const originUrl = ref('') // iframe的url
const productInfo = ref(null) // 产品信息
const isMobileBool = ref(isMobile())

// 隐私政策相关状态
const showPrivacyPolicy = ref(false) // 是否显示隐私政策页面
const privacyPolicyAccepted = ref(false) // 用户是否已接受隐私政策（独立状态变量）

// 隐私政策持久化存储的 key
const PRIVACY_POLICY_ACCEPTED_KEY = 'privacy_policy_accepted'

// 计算属性：判断是否应该显示 LiveChat 组件
const shouldShowLiveChat = computed(() => {
  return componentStatus.value && privacyPolicyAccepted.value && !showPrivacyPolicy.value
})

// 检查隐私政策状态
const checkPrivacyPolicyStatus = () => {
  const { clientUserInfo } = getAllQueryStrings()

  // 1. 特殊处理：当 appId 严格等于字符串 "2" 时，跳过隐私政策检查
  if (appId === '2') {
    console.log('appId=2，跳过隐私政策检查，直接进入聊天')
    privacyPolicyAccepted.value = true
    showPrivacyPolicy.value = false
    return
  }

  // 2. 如果有 clientUserInfo（外部登录用户），跳过隐私政策检查
  if (clientUserInfo) {
    console.log('外部登录用户，跳过隐私政策检查')
    privacyPolicyAccepted.value = true
    return
  }

  // 3. 检查 localStorage 中是否已保存隐私政策接受状态
  const savedAcceptedStatus = localStorage.getItem(PRIVACY_POLICY_ACCEPTED_KEY)

  if (savedAcceptedStatus === 'true') {
    // 用户之前已经接受过隐私政策，直接进入聊天
    console.log('用户之前已接受隐私政策，直接进入聊天')
    privacyPolicyAccepted.value = true
    showPrivacyPolicy.value = false
  } else {
    // 用户未接受过隐私政策，显示隐私政策页面
    console.log('用户未接受过隐私政策，显示隐私政策页面')
    privacyPolicyAccepted.value = false
    showPrivacyPolicy.value = true
  }
}

// 隐私政策操作处理
const handleAcceptPolicy = () => {
  console.log('用户接受了隐私政策')

  // 更新内存中的隐私政策接受状态
  privacyPolicyAccepted.value = true
  showPrivacyPolicy.value = false

  // 将接受状态保存到 localStorage 中实现持久化
  localStorage.setItem(PRIVACY_POLICY_ACCEPTED_KEY, 'true')

  console.log('隐私政策接受状态已保存到 localStorage')
}

const handleRejectPolicy = () => {
  // 拒绝后不进入聊天，保持在隐私政策页面，不保存任何状态到 localStorage
  console.log('用户拒绝了隐私政策')

  // 确保状态保持为未接受
  privacyPolicyAccepted.value = false
  showPrivacyPolicy.value = true

  // 不保存拒绝状态到 localStorage，这样用户下次访问时仍会看到隐私政策页面
}

const handlePrivacyPolicyClose = () => {
  changeChatStatus('close')
}


// 语言设置
setLanguage()
onMounted(() => {
  // 检查隐私政策状态（不建立 WebSocket 连接）
  checkPrivacyPolicyStatus()

  // iframe 通信
  window.addEventListener('message', handleShowLiveChat)
  $bus.on('changeChatStatus', changeChatStatus)
  const storageStatus = localStorage.getItem('liveChatStatus')
  if (storageStatus === '1') {
    // 使用postMessage向父窗口通讯
    window.parent.postMessage({type: 'open_chat', data: 1}, '*')
    componentStatus.value = true
    liveChatStatus.value = true
  }
});
const handleShowLiveChat = (event: MessageEvent) => {
  if (event.data?.type === 'open_chat') {
    // 重新检查隐私政策状态
    checkPrivacyPolicyStatus()

    liveChatStatus.value = true
    componentStatus.value = true
    localStorage.setItem('liveChatStatus', '1')
    originUrl.value = event.data.origin
    window.parent.postMessage({type: 'open_chat', data: 1}, '*')
    window.parent.postMessage({type: 'unread_message', data: false}, '*')
  } else if (event.data === 'hide_chat') {
    liveChatStatus.value = false
    localStorage.setItem('liveChatStatus', '0')
  }
  if (event.data.type === 'prod_card') {
    productInfo.value = event.data.data
  }
}
onBeforeUnmount(() => {
  window.removeEventListener('message', handleShowLiveChat)
})
const changeChatStatus = (status: string) => {
  if (appId === '2') return localStorage.setItem('show_end_btn', 'false')
  window.parent.postMessage('hide_chat', '*')
  localStorage.setItem('liveChatStatus', '0')
  switch (status) {
    case 'minimize':
      setTimeout(() => {
        liveChatStatus.value = false
      }, 300)
      dataLayerToParent({
        eventLabel: "minimize",
      })
      break;
  
    default:
      if (isMobileBool.value && showPrivacyPolicy) {
        showPrivacyPolicy.value = false
        componentStatus.value = false
        return
      }
      setTimeout(() => {
        componentStatus.value = false
        liveChatStatus.value = false
      }, 300)
      dataLayerToParent({
        eventLabel: "close",
      })
      break;
  }
}
function setLanguage() {
  const { locale } = useI18n()
  const site = getQueryString('webSite') || 'en'
  locale.value = site
}
</script>

<template>
  <!-- 隐私政策页面 -->
  <PrivacyPolicy
    v-if="componentStatus && showPrivacyPolicy"
    :isLoading="false"
    @accept-policy="handleAcceptPolicy"
    @reject-policy="handleRejectPolicy"
    @close-chat="handlePrivacyPolicyClose"
  />

  <!-- 正常聊天界面 -->
  <LiveChat
    v-else-if="shouldShowLiveChat"
    @changeChatStatus="changeChatStatus"
    :liveChatStatus="liveChatStatus"
    :originUrl="originUrl"
    :productInfo="productInfo"
  />
</template>

<style lang="scss">

</style>
