.live_chat_wrap {
  position: fixed;
  width: calc(100vw - 32px);
  height: calc(100vh - 32px);
  top: 16px;
  left: 16px;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.15);
  overflow: hidden;
  z-index: 200;
  display: flex;
  flex-direction: column;
  &.isMobile{
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
  }
}

.live_chat_body {
  flex: 1;
  overflow: hidden;

  position: relative;
  .back_bottom_wrap {
      cursor: pointer;
  
      .back_icon {
        position: absolute;
        display: flex;
        align-items: center;
        justify-content: center;
        left: 26px;
        bottom: 10px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        line-height: 18px;
        background: #ccc;
  
        .icon {
          color: #fff;
          font-size: 12px;
        }
  
        &:hover {
          background: #89898c;
        }
      }
  
      .msg_back_icon {
        position: absolute;
        left: 20px;
        bottom: 8px;
        border-radius: 3px;
        padding: 0 8px;
        background: #ccc;
        z-index: 99;
  
        div {
          display: flex;
          height: 32px;
          justify-content: center;
          align-items: center;
          font-size: 12px;
          color: #fff;
  
          .icon {
            width: 12px;
            height: 12px;
            font-size: 12px;
            margin-right: 4px;
          }
        }
  
        &:hover {
          background: #89898c;
        }
      }
    }
  .chat_history_loading {
    width: 100%;
    padding: 20px 0;
    text-align: center;
    & .global_loading {
      display: block;
      width: 100%;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate3d(-50%, -50%, 0);
      text-align: center;
    }
  }
  .live_chat_message_box {
    padding: 16px;
    width: 100%;
    height: 100%;
    overflow: auto;
    overscroll-behavior: none;
    .focus-blur-enter-active {
      animation: text-focus-in 0.5s ease-in 0s 1 normal none;
    }
    
    .focus-blur-leave-active {
      animation: text-blur-out 0.5s linear 0s 1 normal none;
    }
    .typing_wrap {
      display: flex;
      justify-content: center;
      .typing_text {
        @include font12;
        color: #89898C;
      }
    }
    @keyframes text-focus-in {
      0% {
        filter: blur(12px);
        opacity: 0;
      }

      100% {
        filter: blur(0);
        opacity: 1;
      }
    }
    @keyframes text-blur-out {

      0% {
        filter: blur(0);
      }

      100% {
        filter: blur(12px) opacity(0%);
      }
    }
    .sensitive_warning {
      margin: 0 auto;
      margin-bottom: 20px;
      padding: 4px 12px;
      width: fit-content;
      background: #F5F5F9;
      border-radius: 182px;
      .warning_text {
        @include font12;
        color: #707070;
      }
    }
    .wa_wrap {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px 0;
      .link_text {
        @include font12;
        color: #19191A
      }
      .des_text {
        margin-top: 8px;
        .text {
          @include font12;
          color: #89898C;
          position: relative;
          &::before, &::after {
            position: absolute;
            top: 50%;
            content: '';
            display: inline-block;
            width: 50%;
            height: 1px;
            background-color: #E4E7ED;
          }
          &::before {
            right: 100%;
            margin-right: 12px;
          }
          &::after {
            left: 100%;
            margin-left: 12px;
          }
        }
      }
    }
    .form_wrap {
      margin: 0 auto;
      margin-bottom: 16px;
      padding: 20px;
      width: 272px;
      font-size: 12px;
      box-sizing: border-box;
      border-radius: 8px;
      // border: 1px solid #e5e5e5;
      box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
      .input_wrap {
        margin-bottom: 12px;
        &:last-of-type {
          margin-bottom: 0;
        }
        .label {
          display: inline-block;
          margin-bottom: 4px;
        }
        .tel_code {
          width: 100%;
          display: flex;
          align-items: center;
          position: relative;
          &.tel_code_cn {
            .code {
              min-width: auto;
              flex: 0 0 50px;
              text-align: center;
              padding: 0 4px;
              .code_label {
                margin-right: 0;
              }
            }
          }
          .code {
            display: flex;
            align-items: center;
            background: $bgColor1;
            height: 42px;
            border: 1px solid $borderColor2;
            border-right: 0;
            border-radius: 2px 0 0 2px;
            cursor: pointer;
            // min-width: 96px;
            padding: 0 16px;
            flex-shrink: 0;
            .code_label {
              @include font13();
              color: $textColor1;
              flex: 1;
              margin-right: 12px;
            }
          }
          .tel {
            flex: 1 1 auto;
            border-radius: 0 3px 3px 0;
            user-select: text;
            &:focus {
              border-left: 1px solid #19191a;
            }
          }
        }
      }
      .form_submit {
        width: 100%;
        margin-top: 20px;
      }
      .prod_head {
          display: flex;
          justify-content: center;
          border-bottom: 1px solid #e5e5e5;
          padding-bottom: 16px;
          .prod_img {
              width: 50px;
              height: 50px;
              margin-right: 12px;
          }
          .title_wrap {
              flex: 1;
              .txt_container {
                  width: 100%;
                  display: -webkit-box; /* 使用弹性盒子布局 */
                  -webkit-box-orient: vertical; /* 设置盒子内元素垂直排列 */
                  -webkit-line-clamp: 2; /* 限制显示的行数 */
                  overflow: hidden; /* 超出部分隐藏 */
                  text-overflow: ellipsis; /* 超出部分显示省略号 */
                  .prod_title {
                      @include font12;
                      color: #19191a;
                      margin: 0;
                      word-break: break-word;
                  }
              }
              .prod_id {
                  display: block;
                  margin-top: 4px;
                  @include font12;
                  color: #89898c;
              }
          }
      }
      .qa_container {
          padding-top: 16px;
          .qa_wrap {
              display: flex;
              align-items: flex-start;
              margin-bottom: 8px;
              &:last-child {
                  margin: 0;
              }
              .dot {
                  margin-top: 9px;
                  width: 4px;
                  height: 4px;
                  background: #646466;
                  border-radius: 50%;
              }
              .popover {
                position: relative;
                &:hover {
                  .tip {
                    display: block;
                  }
                }
                .tip {
                  position: absolute;
                  @include font13;
                  border-radius: 3px;
                  background: #fff;
                  bottom: 100%;
                  left: 50%;
                  transform: translateX(-50%);
                  cursor: default;
                  box-shadow: 0px 1px 10px 0px rgba(120, 102, 102, 0.3);
                  display: none;
                  margin-bottom: 10px;
                  &::before {
                    display: block;
                    content: '';
                    position: absolute;
                    transform: translateX(-50%);
                    width: 0;
                    height: 0;
                    bottom: -15px;
                    left: 50%;
                    border: 8px solid transparent;
                    border-top: 8px solid #fff;
                  }
                  .tip_text {
                    width: 300px;
                    padding: 20px;
                    color: #646466;
                  }
                }
                .txt_container {
                    max-width: 220px;
                    margin-left: 6px;
                    display: -webkit-box; /* 使用弹性盒子布局 */
                    -webkit-box-orient: vertical; /* 设置盒子内元素垂直排列 */
                    -webkit-line-clamp: 3; /* 限制显示的行数 */
                    overflow: hidden; /* 超出部分隐藏 */
                    text-overflow: ellipsis; /* 超出部分显示省略号 */
                    .qa_text {
                        @include font14;
                        color: #19191a;
                        margin: 0;
                        &:hover {
                            cursor: pointer;
                            text-decoration: underline;
                        }
                    }
                }
              }
          }
      }
    }
    .sg_form_wrap {
      display: flex;
      min-width: 40px;
      max-width: 272px;
      margin: 0 20px 16px 48px;
      padding: 8px 16px;
      background: #f5f5f9;
      border-radius: 0 20px 20px 20px;
      @include font13;
      color: #19191a;
      white-space: pre-line;
      overflow-wrap: break-word;
      // :deep() {
      //     a {
      //         color: #0070bc;
      //         word-wrap: break-word;
      //     }
      // }
      span {
        display: inline-block;
      }
      .input_wrap {
        flex: 1;
        .label {
          @include font12;
          color: #19191a;
          margin-bottom: 4px;
        }
        input {
          background-color: #fff;
          &:disabled {
            background-color: #f7f7f7;
          }
        }
        .suffix {
          position: absolute;
          right: 12px;
          top: 12px;
          width: 16px;
          height: 16px;
          font-size: 16px;
          cursor: pointer;
          &.error {
            color: #c00000;
          }
          &.success {
            color: #10a300;
          }
        }
      }
    }
    .sg_message_info {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 16px;
      .message_chat {
          @include font12;
          color: #89898c;
          text-align: center;
          max-width: 100%;
      }
      .top {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .cs_avator {
          display: inline-block;
          width: 48px;
          height: 48px;
          flex-shrink: 0;
          background: #f5f5f9;
          border: 1.5px solid #eeeeee;
          border-radius: 36px;
          background-repeat: no-repeat;
          background-size: contain;
          background-position: contain;
          position: relative;
        }
        .cs_online_dot {
          position: absolute;
          right: 3px;
          bottom: 0;
          width: 9px;
          height: 9px;
          border-radius: 50%;
          background-color: #10a300;
          border: 1.5px solid #eeeeee;
        }
        .cs_name {
          @include font12;
          color: #19191a;
          margin-top: 4px;
          font-weight: 600;
        }
      }
    }
    .roobot_serve-enter-active,
    .roobot_serve-leave-active {
      transition: opacity 0.2s ease;
    }
    .roobot_serve-enter-from,
    .roobot_serve-leave-to {
      opacity: 0;
    }
    .chat_roobot_serve {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 16px;
      .tag_wrap {
        width: 272px;
        border-radius: 8px;
        // border: 1px solid #e5e5e5;
        border-top: 2px solid #10A300;
        box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.15);
        .chat_roobot_serve_item {
          padding: 12px 24px;
          border-bottom: 1px solid #e5e5e5;
          cursor: pointer;
          &:last-child {
            border: none;
          }
          &:hover {
            background: rgba(25, 25, 26, 0.04);
          }
          .msg_content {
            text-align: center;
            @include font14;
          }
        }
      }
      @keyframes fade-out {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
      }
    }
    .form_wrap {
      margin: 0 auto;
      margin-bottom: 16px;
      padding: 20px;
      width: 272px;
      font-size: 12px;
      box-sizing: border-box;
      border-radius: 8px;
      border-top: 2px solid #10A300;
      box-shadow: 0px 2px 14px 0px rgba(137, 137, 140, 0.15);
      .input_wrap {
        margin-bottom: 12px;
        &:last-of-type {
          margin-bottom: 0;
        }
        .label {
          display: inline-block;
          margin-bottom: 4px;
        }
        .tel_code {
          width: 100%;
          display: flex;
          align-items: center;
          position: relative;
          &.tel_code_cn {
            .code {
              min-width: auto;
              flex: 0 0 50px;
              text-align: center;
              padding: 0 4px;
              .code_label {
                margin-right: 0;
              }
            }
          }
          .code {
            display: flex;
            align-items: center;
            background: $bgColor1;
            height: 42px;
            border: 1px solid $borderColor2;
            border-right: 0;
            border-radius: 2px 0 0 2px;
            cursor: pointer;
            // min-width: 96px;
            padding: 0 16px;
            flex-shrink: 0;
            .code_label {
              @include font13();
              color: $textColor1;
              flex: 1;
              margin-right: 12px;
            }
          }
          .tel {
            flex: 1 1 auto;
            border-radius: 0 3px 3px 0;
            user-select: text;
            &:focus {
              border-left: 1px solid #19191a;
            }
          }
        }
      }
      .form_submit {
        width: 100%;
        margin-top: 20px;
      }
      .prod_head {
        display: flex;
        justify-content: center;
        border-bottom: 1px solid #e5e5e5;
        padding-bottom: 16px;
        .prod_img {
          width: 50px;
          height: 50px;
          margin-right: 12px;
        }
        .title_wrap {
          flex: 1;
          .txt_container {
            width: 100%;
            display: -webkit-box; /* 使用弹性盒子布局 */
            -webkit-box-orient: vertical; /* 设置盒子内元素垂直排列 */
            -webkit-line-clamp: 2; /* 限制显示的行数 */
            overflow: hidden; /* 超出部分隐藏 */
            text-overflow: ellipsis; /* 超出部分显示省略号 */
            .prod_title {
              @include font12;
              color: #19191a;
              margin: 0;
              word-break: break-word;
            }
          }
          .prod_id {
            display: block;
            margin-top: 4px;
            @include font12;
            color: #89898c;
          }
        }
      }
    }
    .text_wrap {
      display: flex;
      min-width: 40px;
      max-width: 272px;
      margin: 0 20px 16px 48px;
      padding: 8px 16px;
      background: #f5f5f9;
      border-radius: 0 20px 20px 20px;
      @include font13;
      color: #19191a;
      white-space: pre-line;
      overflow-wrap: break-word;
      &.cs_text_break {
        word-break: break-all;
      }
      :deep(a) {
          color: #0070bc;
          word-wrap: break-word;
      }
      span {
        display: inline-block;
      }
      .input_wrap {
        flex: 1;
        .label {
          @include font12;
          color: #19191a;
          margin-bottom: 4px;
        }
        input {
          background-color: #fff;
          &:disabled {
            background-color: #f7f7f7;
          }
        }
        .suffix {
          position: absolute;
          right: 12px;
          top: 12px;
          width: 16px;
          height: 16px;
          font-size: 16px;
          cursor: pointer;
          &.error {
            color: #c00000;
          }
          &.success {
            color: #10a300;
          }
        }
        .sign_in {
          margin-top: 4px;
        }
      }
    }
    .message_sys_wrap {
      margin-bottom: 16px;
    }
    .message_sys_box {
      margin: 0 auto;
      max-width: 300px;
      text-align: center;
      .message_admin_time {
        text-align: center;
        color: #999999;
        @include font12;
        margin-bottom: 8px;
      }
      .message_admin_msg_box {
        display: inline-block;
        max-width: 100%;
        width: auto;
        background: #89898C;
        padding: 3px;
        min-height: 26px;
        border-radius: 24px;
        margin: 0 auto;
        position: relative;
        padding: 4px 16px 4px 30px;
        .iconfont_success {
          display: inline-block;
          width: 18px;
          height: 18px;
          flex-shrink: 0;
          // font-size: 18px;
          color: #fff;
          flex-shrink: 0;
          position: absolute;
          left: 4px;
          top: 50%;
          transform: translate3d(0, -50%, 0);
        }
        .message_admin_msg {
          @include font12;
          color: #fff;
          text-align: left;
        }
      }
      @media (max-width: 960px) {
        width: 86.7%;
        max-width: none;
      }
    }
    .message_create_box {
      display: flex;
      justify-content: center;
      margin-bottom: 16px;
      .message_chat {
        @include font12;
        color: #89898c;
        text-align: center;
        max-width: 100%;
      }
      .privacy {
        width: 300px;
        margin: 8px 0;
      }
      &.column {
        flex-direction: column;
        align-items: center;
      }
      .top {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .cs_avator {
          display: inline-block;
          width: 48px;
          height: 48px;
          flex-shrink: 0;
          background: #f5f5f9;
          border: 1.5px solid #eeeeee;
          border-radius: 36px;
          background-repeat: no-repeat;
          background-size: contain;
          background-position: contain;
          position: relative;
        }
        .cs_online_dot {
          position: absolute;
          right: 3px;
          bottom: 0;
          width: 9px;
          height: 9px;
          border-radius: 50%;
          background-color: #10a300;
          border: 1.5px solid #eeeeee;
        }
        .cs_name {
          @include font12;
          color: #19191a;
          margin-top: 4px;
          font-weight: 600;
        }
      }
    }
    .message_cs_wrap {
      display: flex;
      align-items: flex-start;
      padding-right: 20px;
      margin-bottom: 16px;
      .cs_avator {
        display: inline-block;
        width: 36px;
        height: 36px;
        flex-shrink: 0;
        background: #f5f5f9;
        border: 1.5px solid #eeeeee;
        border-radius: 36px;
        margin-right: 8px;
        background-repeat: no-repeat;
        background-size: contain;
        background-position: contain;
      }
      .message_cs_box {
        flex: 1;
        .cs_name {
          @include font12;
          color: #89898c;
          margin-bottom: 4px;
        }
        .cs_text {
          display: inline-block;
          min-width: 40px;
          max-width: 272px;
          padding: 8px 16px;
          background: #f5f5f9;
          border-radius: 0 20px 20px 20px;
          @include font14;
          color: #19191a;
          white-space: pre-line;
          overflow-wrap: break-word;
          &.cs_text_break {
            word-break: break-all;
          }
          a {
            color: #0070bc;
            word-wrap: break-word;
          }
        }
        .cs_text_loading {
          width: 60px;
          height: 20px;
          display: flex;
          justify-content: center;
          padding: 20px;
          background: #f5f5f9;
          border-radius: 0 20px 20px 20px;
        }
        .cs_img {
          display: inline-block;
          width: 160px;
          height: 160px;
          border-radius: 0 20px 20px 20px;
          background: #fff;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
          cursor: pointer;
        }
        .cs_video {
          video {
            max-width: 300px;
            max-height: 200px;
          }
        }
        .cs_file {
          background: #fff;
          box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
          border-radius: 0px 20px 20px 20px;
          border: 1px solid #f2f2f2;
          padding: 8px 12px 10px 12px;
          display: flex;
          align-items: center;
          cursor: pointer;
          width: 240px;
          text-align: left;
          .file_icon {
            display: inline-block;
            width: 48px;
            height: 48px;
            flex-shrink: 0;
            margin-right: 8px;
          }
          .file_info {
            flex: 1 1 auto;
            .file_name {
              color: #232323;
              @include font13;
              margin-bottom: 6px;
              word-break: break-all;
            }
            .file_size {
              color: #999999;
              @include font12;
            }
          }
        }
      }
    }
    .message_user_wrap {
      display: flex;
      align-items: flex-start;
      padding-left: 5.4%;
      margin-bottom: 16px;
      flex-direction: row-reverse;
      text-align: right;
      .message_user_box {
        .cs_name {
          @include font12;
          color: #89898c;
          margin-bottom: 4px;
          text-align: right;
        }
        .cs_text {
          display: inline-block;
          min-width: 40px;
          max-width: 272px;
          padding: 8px 16px;
          background: #10A300;
          border-radius: 20px 0 20px 20px;
          text-align: left;
          @include font14;
          color: #fff;
          white-space: pre-line;
          overflow-wrap: break-word;
          &.cs_text_break {
            word-break: break-all;
          }
          :deep(a) {
              color: #fff;
              word-wrap: break-word;
          }
        }
        .cs_img {
          display: inline-block;
          width: 160px;
          height: 160px;
          border-radius: 20px 0 20px 20px;
          background: #fff;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
          cursor: pointer;
        }
        .cs_video {
          video {
            max-width: 300px;
            max-height: 200px;
          }
        }
        .cs_file {
          background: #fff;
          box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
          border-radius: 20px 0 20px 20px;
          border: 1px solid #f2f2f2;
          padding: 8px 12px 10px 12px;
          display: flex;
          align-items: center;
          cursor: pointer;
          width: 240px;
          text-align: left;
          .file_icon {
            display: inline-block;
            width: 48px;
            height: 48px;
            flex-shrink: 0;
            margin-right: 8px;
          }
          .file_info {
            flex: 1 1 auto;
            .file_name {
              color: #232323;
              @include font13;
              margin-bottom: 6px;
              word-break: break-all;
            }
            .file_size {
              color: #999999;
              @include font12;
            }
          }
        }
      }
    }
    .message_wrap {
      &:last-child {
        margin-bottom: 0;
      }
    }
    .rate_feedback_box {
      width: 272px;
      margin: 0 auto;
      margin-bottom: 16px;
      background: #ffffff;
      box-shadow: 0px 2px 8px 0px rgba(195, 195, 206, 0.24);
      border-radius: 3px;
      border: 1px solid #f2f2f2;
      padding: 20px 16px;
      .rate_box {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #efefef;
        .rate_title {
          @include font12;
          color: #19191a;
          margin-bottom: 12px;
        }
        .rate_btn_box {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .rate_btn {
            width: calc((100% - 12px) / 2);
            // height: 32px;
            color: #19191a;
            border: 1px solid #19191a;
            position: relative;
            &.rate_btn_good {
              color: #4b4b4d;
              border: 1px solid #4b4b4d;
              &.rate_btn_not {
                cursor: not-allowed;
                &:hover {
                  color: #4b4b4d;
                  border: 1px solid #4b4b4d;
                  background: #fff;
                }
              }
            }
            &.rate_btn_active {
              color: #fff;
              border: 1px solid #4b4b4d;
              background: #4b4b4d;
            }
            &.rate_btn_not {
              cursor: not-allowed;
              &:hover {
                color: #999;
                border: 1px solid #cccccc;
                background: #fff;
              }
            }
            &:hover {
              color: $textColor1;
              border: 1px solid #4b4b4d;
              background: rgba(25, 25, 26, 0.04);
              .rate_tip {
                display: block;
              }
            }
            .iconfont {
              margin-right: 8px;
              width: 16px;
              height: 16px;
            }
            .rate_tip {
              position: absolute;
              padding: 8px 12px;
              color: #000;
              @include font14;
              border-radius: 4px;
              white-space: nowrap;
              background: #fff;
              top: -48px;
              left: 50%;
              transform: translate3d(-50%, 0, 0);
              cursor: default;
              box-shadow: 0px 1px 10px 0px rgba(120, 102, 102, 0.3);
              display: none;
              &::before {
                display: block;
                content: '';
                position: absolute;
                transform: translateX(-50%);
                width: 0;
                height: 0;
                bottom: -15px;
                left: 50%;
                border: 8px solid transparent;
                border-top: 8px solid #fff;
              }
            }
          }
        }
        .has_voted {
          display: flex;
          align-items: center;
          @include font12;
          color: #19191a;
          margin-top: 8px;
          .iconfont_success {
            font-size: 16px;
            color: #10a300;
            margin-right: 8px;
            flex-shrink: 0;
            display: inline-block;
          }
        }
      }
      .feedback_box {
        display: flex;
        flex-direction: column;
        .feedback_title {
          @include font12;
          color: #19191a;
          margin-bottom: 8px;
        }
        .feedback_textarea {
          height: 74px;
          overflow: auto;
          margin-bottom: 16px;
        }
        button {
          align-self: flex-end;
        }
        .sg_email_input {
          padding: 16px 0;
          border-top: 1px solid #efefef;
        }
      }
    }
  }
}
.live_chat_global_loading {
    display: block;
    width: 100%;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate3d(-50%, -50%, 0);
    text-align: center;
}
.live_chat_mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.3);
}
.end_chat_box {
  display: flex;
  flex-direction: column;
  padding: 30px 20px;
  background: #fff;
  border-radius: 3px;
  position: absolute;
  width: 86.7%;
  max-width: 300px;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  .end_chat_icon_wrap {
    position: absolute;
    top: 16px;
    right: 16px;
    width: 32px;
    height: 32px;
    border-radius: 3px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #707070;
    &:hover {
        color: #19191a;
        background-color: rgba($color: #19191A, $alpha: 0.04);
      }
    .end_chat_close {
      width: 16px;
      height: 16px;
      cursor: pointer;
      transition: all 0.3s;
    }
  }
  .end_chat_title {
    color: #19191a;
    @include font16;
    font-weight: 600;
  }
  .end_chat_desc {
    @include font14;
    color: #19191a;
    margin: 8px 0 28px;
  }
  button {
    align-self: flex-end;
  }
}
.video_tip {
  background: #fff;
  border-radius: 8px;
  position: absolute;
  width: 86.7%;
  max-width: 280px;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  color: #19191A;
  .title_wrap {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E5E5E5;
    .title {
      @include font16;
    }
    .iconfont_close {
      display: inline-block;
      width: 16px;
      height: 16px;
      font-size: 16px;
      cursor: pointer;
    }
  }
  .content {
    padding: 20px 16px;
    @include font14;
  }
  .btn_wrap {
    width: 100%;
    padding-bottom: 16px;
    display: flex;
    justify-content: center;
  }
}
.address_modal {
    background: #fff;
    border-radius: 8px;
    position: absolute;
    width: 86.7%;
    max-width: 280px;
    top: 50%;
    left: 50%;
    transform: translate3d(-50%, -50%, 0);
    color: #19191A;
    .title_wrap {
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #E5E5E5;
    .title {
      @include font16;
    }
    .icon_wrap {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #707070;
      cursor: pointer;
      &:hover {
        color: #19191a;
        background-color: rgba($color: #19191a, $alpha: 0.04);
        border-radius: 3px;
      }
    }
    .iconfont_close {
      width: 16px;
      height: 16px;
      font-size: 16px;
    }
  }
  .address_modal_content {
    padding: 20px 16px 16px 16px;
    display: flex;
    flex-direction: column;
  }
    textarea {
        width: 100%;
        height: 68px;
    }
    .info {
        display: flex;
        flex-direction: column;
        width: 100%;
        justify-content: center;
        align-items: center;
        img {
            width: 48px;
            height: 48px;
        }
    }
    .error {
        @include font13;
        color: #c00000;
        margin-top: 4px;
    }
    .tips_wrap {
        @include font13;
        color: #89898c;
        margin: 4px 0 36px;
    }
    button {
        align-self: center;
    }
    .back_btn {
        align-self: center;
    }
}
.feedback_container {
  .feedback_head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #E5E5E5;
    .feedback_title {
      @include font16;
      font-weight: 600;
      color: #19191a;
      padding: 0 16px;
    }
    .feedback_close {
      display: flex;
      padding: 16px;
      cursor: pointer;
      .iconfont_close {
        font-size: 20px;
      }
    }
  }
  .feedback_body {
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .rate_box {
      display: flex;
      gap: 32px;
      margin-bottom: 20px;
      & > .iconfont {
        font-size: 30px;
        color: #707070;
        cursor: pointer;
      }
      .iconfont_good {
        color: #10a300;
      }
      .iconfont_bad {
        color: #c00000;
      }
    }
    .feedback_input {
      height: 36px;
      margin-bottom: 20px;
    }
  }
}