const fs = require('fs')
const path = require('path')
//复制sdk.js到dist文件夹
const copySdk = () => {
  const env = process.argv[2]
  const sdkPath = path.resolve(__dirname, './public/sdk.js')
  const envFilePath = path.resolve(__dirname, `./.env.${env}`)
  const sdkContent = fs.readFileSync(sdkPath, 'utf-8')
  const envContent = fs.readFileSync(envFilePath, 'utf-8')
  // 解析环境变量内容为对象
  const sdkUrl = envContent.split('\n')[0].replace(/\s/g, '')
  const sdkUrlContent = sdkUrl.split('=')[1]
  console.log(sdkUrlContent)
  const str = sdkContent.replace('$_path', sdkUrlContent)
  console.log(str)
  //存在dist/sdk.js则删除,不存在则创建文件
  const distSdkPath = path.resolve(__dirname, './dist/sdk.js')
  if (!fs.existsSync(distSdkPath)) {
    fs.writeFileSync(distSdkPath, '')
  }
  //写入sdk.js
  fs.writeFileSync(distSdkPath, str)
}
copySdk()
