# Vue 3 + TypeScript + Vite

This template should help get you started developing with Vue 3 and TypeScript in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.

## Recommended IDE Setup

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur) + [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin).

## Type Support For `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [TypeScript Vue Plugin (Volar)](https://marketplace.visualstudio.com/items?itemName=Vue.vscode-typescript-vue-plugin) to make the TypeScript language service aware of `.vue` types.

If the standalone TypeScript plugin doesn't feel fast enough to you, Volar has also implemented a [Take Over Mode](https://github.com/johnsoncodehk/volar/discussions/471#discussioncomment-1361669) that is more performant. You can enable it by the following steps:

1. Disable the built-in TypeScript Extension
   1. Run `Extensions: Show Built-in Extensions` from VSCode's command palette
   2. Find `TypeScript and JavaScript Language Features`, right click and select `Disable (Workspace)`
2. Reload the VSCode window by running `Developer: Reload Window` from the command palette.

```
livechat-package-frontend
├─ .env.development
├─ .env.production
├─ .env.test
├─ .gitignore
├─ Dockerfile
├─ README.md
├─ buildSdk.cjs
├─ index.html
├─ nginx.conf
├─ package.json
├─ public
│  ├─ sdk.js
│  └─ vite.svg
├─ src
│  ├─ App.vue
│  ├─ assets
│  │  ├─ emoji-list.js
│  │  ├─ iconfont
│  │  ├─ scss
│  │  │  ├─ flexible.scss
│  │  │  ├─ global.scss
│  │  │  ├─ mixin.scss
│  │  │  └─ reset.scss
│  │  ├─ sensi_words.json
│  │  └─ svg
│  │     ├─ app
│  │     ├─ doc.svg
│  │     ├─ email_sent.svg
│  │     ├─ pdf.svg
│  │     ├─ txt.svg
│  │     └─ xls.svg
│  ├─ components
│  │  ├─ DropDown.vue
│  │  ├─ Emoji.vue
│  │  ├─ FsButton.vue
│  │  ├─ GlobalLoading.vue
│  │  ├─ MenuPopWrap.vue
│  │  ├─ MessageLoading.vue
│  │  └─ ValidateError.vue
│  ├─ global.d.ts
│  ├─ lang
│  │  ├─ cn
│  │  │  └─ index.ts
│  │  ├─ de
│  │  │  └─ index.ts
│  │  ├─ en
│  │  │  └─ index.ts
│  │  ├─ es
│  │  │  └─ index.ts
│  │  ├─ fr
│  │  │  └─ index.ts
│  │  ├─ index.ts
│  │  ├─ it
│  │  │  └─ index.ts
│  │  ├─ jp
│  │  │  └─ index.ts
│  │  └─ ru
│  │     └─ index.ts
│  ├─ main.ts
│  ├─ plugins
│  │  ├─ c-inject.ts
│  │  ├─ gtm.ts
│  │  └─ socket.ts
│  ├─ style
│  │  └─ liveChat.scss
│  ├─ style.css
│  ├─ util
│  │  ├─ FsWs.ts
│  │  ├─ base64.ts
│  │  ├─ byteBuffer.ts
│  │  ├─ crypto.ts
│  │  ├─ eventBus.ts
│  │  ├─ flexible.ts
│  │  ├─ geolocation.ts
│  │  ├─ request.ts
│  │  └─ util.ts
│  ├─ views
│  │  ├─ LiveChat.vue
│  │  └─ components
│  │     ├─ MessageBody
│  │     │  └─ MessageBody.vue
│  │     ├─ MessageHeader
│  │     │  ├─ index.scss
│  │     │  └─ index.vue
│  │     ├─ MobileFooter
│  │     │  └─ index.vue
│  │     └─ messageItem
│  │        └─ messageItem.vue
│  └─ vite-env.d.ts
├─ tsconfig.json
├─ tsconfig.node.json
└─ vite.config.ts

```