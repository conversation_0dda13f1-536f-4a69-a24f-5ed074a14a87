import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'node:path'

// https://vitejs.dev/config/
export default defineConfig(({mode}) => {
  const env = loadEnv(mode, process.cwd(), '')
  console.log(env.VITE_CDN_URL)
  return {
    base: env.VITE_CDN_URL, // 打包之后替换成cdn地址
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'), // 路径别名
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',// vue3使用vue-i18n需要配置
      },
      extensions: ['.js', '.json', '.ts'],
    },
    server: {
      host: '0.0.0.0',
      port: 5174,
      open: true,
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "./src/assets/scss/global.scss";`, // 导入全局的 SCSS 变量文件
        },
      },
    },
    esbuild: {
      drop: mode === 'production' ? ['console', 'debugger'] : [], // 生产环境移除console
    },
  }
})
