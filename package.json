{"name": "chat-demo", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "dev:prod": "vite --mode production", "build:test": "vue-tsc && vite build --mode test  && npm run copy:test", "build": "vue-tsc && vite build && npm run copy:prod", "preview": "vite preview", "copy:test": "node ./buildSdk.cjs test", "copy:prod": "node ./buildSdk.cjs production"}, "dependencies": {"@types/node": "^20.2.4", "@unhead/vue": "^1.9.16", "axios": "^1.4.0", "chinese-simple2traditional": "^1.1.0", "crypto-js": "^4.1.1", "mint-filter": "^4.0.3", "vue": "^3.2.47", "vue-i18n": "^9.2.2", "xss": "^1.0.14"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@vitejs/plugin-vue": "^4.1.0", "sass": "^1.62.1", "typescript": "5.6.2", "vite": "^4.3.2", "vue-tsc": "^2.0.29"}}